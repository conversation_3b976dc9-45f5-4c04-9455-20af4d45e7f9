import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Eye, Edit, Search, Filter, Camera, FileText, MessageSquare, Plus, Upload } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Vehicle {
  id: string;
  plateNumber: string;
  vehicleInfo: string;
  customerName: string;
  servicesInProgress: string[];
  status: 'in_progress' | 'ready' | 'postponed';
  entryDate: string;
  expectedExitDate: string;
  internalNotes: string;
}

const mockVehicles: Vehicle[] = [
  {
    id: '1',
    plateNumber: 'أ ب ج 123',
    vehicleInfo: 'تويوتا كامري 2020 - أبيض',
    customerName: 'أحمد محمد علي',
    servicesInProgress: ['تغيير زيت', 'فحص شامل'],
    status: 'in_progress',
    entryDate: '2024-07-10',
    expectedExitDate: '2024-07-15',
    internalNotes: 'العميل طلب تنظيف إضافي - يدفع نقداً'
  },
  {
    id: '2',
    plateNumber: 'د ه و 456',
    vehicleInfo: 'هوندا أكورد 2019 - أسود',
    customerName: 'فاطمة حسن',
    servicesInProgress: ['إصلاح مكابح'],
    status: 'ready',
    entryDate: '2024-07-08',
    expectedExitDate: '2024-07-12',
    internalNotes: 'جاهز للاستلام - تم الاتصال بالعميل'
  },
  {
    id: '3',
    plateNumber: 'ز ح ط 789',
    vehicleInfo: 'نيسان التيما 2021 - فضي',
    customerName: 'محمد سالم',
    servicesInProgress: ['صيانة دورية', 'إصلاح تكييف'],
    status: 'postponed',
    entryDate: '2024-07-05',
    expectedExitDate: '2024-07-20',
    internalNotes: 'في انتظار قطع غيار من المورد'
  },
  {
    id: '4',
    plateNumber: 'ي ك ل 321',
    vehicleInfo: 'مرسيدس C-Class 2018 - أزرق',
    customerName: 'سارة أحمد',
    servicesInProgress: ['إصلاح كهرباء', 'تنظيف شامل'],
    status: 'in_progress',
    entryDate: '2024-07-12',
    expectedExitDate: '2024-07-18',
    internalNotes: 'عميل VIP - عناية خاصة مطلوبة'
  }
];

const getStatusBadge = (status: Vehicle['status']) => {
  const statusConfig = {
    in_progress: { label: 'قيد الإصلاح', color: 'bg-orange-500/10 text-orange-700 border-orange-500/20' },
    ready: { label: 'جاهز للاستلام', color: 'bg-green-500/10 text-green-700 border-green-500/20' },
    postponed: { label: 'مؤجل', color: 'bg-red-500/10 text-red-700 border-red-500/20' }
  };

  const config = statusConfig[status];
  return (
    <Badge className={`font-arabic ${config.color}`}>
      {config.label}
    </Badge>
  );
};

const Vehicles = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [vehicles, setVehicles] = useState(mockVehicles);
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const [newNote, setNewNote] = useState("");
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const { toast } = useToast();

  const filteredVehicles = vehicles.filter(vehicle => {
    const matchesSearch = vehicle.plateNumber.includes(searchTerm) || 
                         vehicle.customerName.includes(searchTerm) ||
                         vehicle.vehicleInfo.includes(searchTerm);
    const matchesStatus = statusFilter === "all" || vehicle.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleViewDetails = (vehicle: Vehicle) => {
    setSelectedVehicle(vehicle);
    setNewNote(vehicle.internalNotes);
    setIsDetailsModalOpen(true);
  };

  const handleSaveNotes = () => {
    if (selectedVehicle) {
      setVehicles(vehicles.map(v => 
        v.id === selectedVehicle.id 
          ? { ...v, internalNotes: newNote }
          : v
      ));
      toast({
        title: "تم حفظ الملاحظات",
        description: "تم حفظ الملاحظات الداخلية بنجاح",
      });
    }
  };

  const handleStatusUpdate = (vehicle: Vehicle, newStatus: Vehicle['status']) => {
    setVehicles(vehicles.map(v => 
      v.id === vehicle.id 
        ? { ...v, status: newStatus }
        : v
    ));
    toast({
      title: "تم تحديث الحالة",
      description: `تم تحديث حالة المركبة ${vehicle.plateNumber}`,
    });
  };

  const handleUploadImages = (vehicle: Vehicle) => {
    toast({
      title: "تم رفع الصور",
      description: `تم رفع صور إصلاح للمركبة ${vehicle.plateNumber}`,
    });
  };

  const handleGenerateReport = (vehicle: Vehicle) => {
    toast({
      title: "تم إنشاء التقرير",
      description: `تم إنشاء تقرير PDF للمركبة ${vehicle.plateNumber}`,
    });
  };

  const handleQuickEdit = (vehicle: Vehicle) => {
    toast({
      title: "تحديث سريع",
      description: `تم فتح نافذة التحديث السريع للمركبة ${vehicle.plateNumber}`,
    });
  };

  const handleAddMessage = (vehicle: Vehicle) => {
    toast({
      title: "إضافة رسالة",
      description: `تم فتح نافذة الرسائل للمركبة ${vehicle.plateNumber}`,
    });
  };

  return (
    <div className="flex-1 space-y-6 p-6" dir="rtl">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight text-foreground font-arabic">
          المركبات داخل الكراج
        </h1>
        <p className="text-muted-foreground font-arabic">
          متابعة وإدارة جميع المركبات الموجودة حالياً في الكراج
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-foreground">{vehicles.length}</p>
              <p className="text-sm text-muted-foreground font-arabic">إجمالي المركبات</p>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-orange-600">
                {vehicles.filter(v => v.status === 'in_progress').length}
              </p>
              <p className="text-sm text-muted-foreground font-arabic">قيد الإصلاح</p>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {vehicles.filter(v => v.status === 'ready').length}
              </p>
              <p className="text-sm text-muted-foreground font-arabic">جاهز للاستلام</p>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-card/50 backdrop-blur-sm border-border/50">
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600">
                {vehicles.filter(v => v.status === 'postponed').length}
              </p>
              <p className="text-sm text-muted-foreground font-arabic">مؤجل</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card className="bg-card/50 backdrop-blur-sm border-border/50">
        <CardHeader>
          <CardTitle className="text-foreground font-arabic">البحث والتصفية</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="البحث عن لوحة أو عميل أو سيارة..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10 font-arabic"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-48 font-arabic">
                <Filter className="h-4 w-4 ml-2" />
                <SelectValue placeholder="حالة المركبة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all" className="font-arabic">جميع المركبات</SelectItem>
                <SelectItem value="in_progress" className="font-arabic">قيد الإصلاح</SelectItem>
                <SelectItem value="ready" className="font-arabic">جاهز للاستلام</SelectItem>
                <SelectItem value="postponed" className="font-arabic">مؤجل</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Vehicles Table */}
      <Card className="bg-card/50 backdrop-blur-sm border-border/50">
        <CardHeader>
          <CardTitle className="text-foreground font-arabic">قائمة المركبات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-right font-arabic">رقم اللوحة</TableHead>
                  <TableHead className="text-right font-arabic">نوع المركبة</TableHead>
                  <TableHead className="text-right font-arabic">اسم العميل</TableHead>
                  <TableHead className="text-right font-arabic">الخدمات الجارية</TableHead>
                  <TableHead className="text-right font-arabic">الحالة</TableHead>
                  <TableHead className="text-right font-arabic">تاريخ الدخول</TableHead>
                  <TableHead className="text-right font-arabic">التاريخ المتوقع للخروج</TableHead>
                  <TableHead className="text-right font-arabic">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredVehicles.map((vehicle) => (
                  <TableRow key={vehicle.id} className="hover:bg-muted/50">
                    <TableCell className="font-mono font-medium">{vehicle.plateNumber}</TableCell>
                    <TableCell className="font-arabic">{vehicle.vehicleInfo}</TableCell>
                    <TableCell className="font-arabic">{vehicle.customerName}</TableCell>
                    <TableCell className="font-arabic">
                      <div className="flex flex-wrap gap-1">
                        {vehicle.servicesInProgress.map((service, index) => (
                          <Badge key={index} variant="outline" className="text-xs font-arabic">
                            {service}
                          </Badge>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(vehicle.status)}</TableCell>
                    <TableCell className="font-mono">{vehicle.entryDate}</TableCell>
                    <TableCell className="font-mono">{vehicle.expectedExitDate}</TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-8 w-8 p-0"
                          onClick={() => handleViewDetails(vehicle)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-8 w-8 p-0"
                          onClick={() => handleQuickEdit(vehicle)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-8 w-8 p-0"
                          onClick={() => handleUploadImages(vehicle)}
                        >
                          <Camera className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-8 w-8 p-0"
                          onClick={() => handleGenerateReport(vehicle)}
                        >
                          <FileText className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-8 w-8 p-0"
                          onClick={() => handleAddMessage(vehicle)}
                        >
                          <MessageSquare className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Vehicle Details Modal */}
      <Dialog open={isDetailsModalOpen} onOpenChange={setIsDetailsModalOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto" dir="rtl">
          <DialogHeader>
            <DialogTitle className="font-arabic">تفاصيل المركبة</DialogTitle>
          </DialogHeader>
          {selectedVehicle && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium font-arabic block mb-2">رقم اللوحة</label>
                  <p className="text-lg font-mono font-bold">{selectedVehicle.plateNumber}</p>
                </div>
                <div>
                  <label className="text-sm font-medium font-arabic block mb-2">نوع المركبة</label>
                  <p className="font-arabic">{selectedVehicle.vehicleInfo}</p>
                </div>
                <div>
                  <label className="text-sm font-medium font-arabic block mb-2">اسم العميل</label>
                  <p className="font-arabic">{selectedVehicle.customerName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium font-arabic block mb-2">الحالة الحالية</label>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(selectedVehicle.status)}
                    <Select
                      value={selectedVehicle.status}
                      onValueChange={(value: Vehicle['status']) => 
                        handleStatusUpdate(selectedVehicle, value)
                      }
                    >
                      <SelectTrigger className="w-40 font-arabic">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="in_progress" className="font-arabic">قيد الإصلاح</SelectItem>
                        <SelectItem value="ready" className="font-arabic">جاهز للاستلام</SelectItem>
                        <SelectItem value="postponed" className="font-arabic">مؤجل</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium font-arabic block mb-2">تاريخ الدخول</label>
                  <p className="font-mono">{selectedVehicle.entryDate}</p>
                </div>
                <div>
                  <label className="text-sm font-medium font-arabic block mb-2">التاريخ المتوقع للخروج</label>
                  <p className="font-mono">{selectedVehicle.expectedExitDate}</p>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium font-arabic block mb-2">الخدمات الجارية</label>
                <div className="flex flex-wrap gap-2">
                  {selectedVehicle.servicesInProgress.map((service, index) => (
                    <Badge key={index} variant="outline" className="font-arabic">
                      {service}
                    </Badge>
                  ))}
                </div>
              </div>

              <div>
                <label className="text-sm font-medium font-arabic block mb-2">الملاحظات الداخلية</label>
                <Textarea 
                  value={newNote}
                  onChange={(e) => setNewNote(e.target.value)}
                  className="font-arabic"
                  placeholder="أضف ملاحظات داخلية..."
                  rows={4}
                />
              </div>

              <div className="flex flex-wrap gap-2">
                <Button onClick={handleSaveNotes} className="font-arabic">
                  <Plus className="h-4 w-4 ml-2" />
                  حفظ الملاحظات
                </Button>
                <Button 
                  variant="outline" 
                  className="font-arabic"
                  onClick={() => handleUploadImages(selectedVehicle)}
                >
                  <Camera className="h-4 w-4 ml-2" />
                  رفع صور الإصلاح
                </Button>
                <Button 
                  variant="outline" 
                  className="font-arabic"
                  onClick={() => handleGenerateReport(selectedVehicle)}
                >
                  <FileText className="h-4 w-4 ml-2" />
                  إنشاء تقرير PDF
                </Button>
                <Button 
                  variant="outline" 
                  className="font-arabic"
                  onClick={() => {
                    toast({
                      title: "رفع ملف",
                      description: "تم فتح نافذة رفع الملفات",
                    });
                  }}
                >
                  <Upload className="h-4 w-4 ml-2" />
                  رفع ملفات
                </Button>
              </div>

              {/* Work History */}
              <div className="border-t pt-4">
                <h3 className="font-arabic font-semibold mb-3">سجل أعمال الإصلاح</h3>
                <div className="space-y-2 bg-muted/30 p-4 rounded-lg">
                  <div className="text-sm">
                    <span className="font-medium font-arabic">2024-07-12 10:30:</span>
                    <span className="font-arabic mr-2">بدء فحص شامل للمحرك</span>
                  </div>
                  <div className="text-sm">
                    <span className="font-medium font-arabic">2024-07-12 14:15:</span>
                    <span className="font-arabic mr-2">تم تغيير زيت المحرك والفلتر</span>
                  </div>
                  <div className="text-sm">
                    <span className="font-medium font-arabic">2024-07-13 09:00:</span>
                    <span className="font-arabic mr-2">فحص نظام المكابح - سليم</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Vehicles;