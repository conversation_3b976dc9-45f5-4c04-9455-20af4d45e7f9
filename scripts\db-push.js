#!/usr/bin/env node

/**
 * Database Push Script
 * This script helps push the database schema to Supabase
 */

import { createClient } from '@supabase/supabase-js'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Load environment variables
import dotenv from 'dotenv'
dotenv.config()

const supabaseUrl = process.env.VITE_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Make sure VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in .env')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function runMigration() {
  try {
    console.log('🚀 Starting database migration...')
    
    // Read the migration file
    const migrationPath = path.join(__dirname, '../supabase/migrations/20241210000001_initial_schema.sql')
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')
    
    console.log('📄 Running migration SQL...')
    
    // Execute the migration
    const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL })
    
    if (error) {
      console.error('❌ Migration failed:', error)
      process.exit(1)
    }
    
    console.log('✅ Migration completed successfully!')
    
    // Run seed data
    console.log('🌱 Running seed data...')
    const seedPath = path.join(__dirname, '../supabase/seed.sql')
    const seedSQL = fs.readFileSync(seedPath, 'utf8')
    
    const { error: seedError } = await supabase.rpc('exec_sql', { sql: seedSQL })
    
    if (seedError) {
      console.error('❌ Seed failed:', seedError)
      console.log('⚠️  Migration completed but seed data failed')
    } else {
      console.log('✅ Seed data completed successfully!')
    }
    
    console.log('🎉 Database setup complete!')
    console.log('')
    console.log('Next steps:')
    console.log('1. Create admin and garage owner accounts in Supabase Auth dashboard')
    console.log('2. Update garage owner_id fields with actual user IDs')
    console.log('3. Test the authentication flow')
    
  } catch (error) {
    console.error('❌ Unexpected error:', error)
    process.exit(1)
  }
}

// Alternative function to execute SQL directly
async function executeSQLFile(filePath) {
  try {
    const sql = fs.readFileSync(filePath, 'utf8')
    
    // Split SQL into individual statements
    const statements = sql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0)
    
    console.log(`📄 Executing ${statements.length} SQL statements...`)
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i]
      if (statement.trim()) {
        console.log(`⏳ Executing statement ${i + 1}/${statements.length}`)
        
        const { error } = await supabase.rpc('exec_sql', { sql: statement })
        
        if (error) {
          console.error(`❌ Error in statement ${i + 1}:`, error)
          console.error('Statement:', statement.substring(0, 100) + '...')
          throw error
        }
      }
    }
    
    console.log('✅ All statements executed successfully!')
    
  } catch (error) {
    console.error('❌ SQL execution failed:', error)
    throw error
  }
}

// Check command line arguments
const command = process.argv[2]

switch (command) {
  case 'migrate':
    runMigration()
    break
  case 'seed':
    executeSQLFile(path.join(__dirname, '../supabase/seed.sql'))
    break
  case 'reset':
    console.log('⚠️  This will drop all tables and recreate them!')
    console.log('Make sure you want to do this in production!')
    // Add confirmation logic here
    runMigration()
    break
  default:
    console.log('Usage: node db-push.js [migrate|seed|reset]')
    console.log('')
    console.log('Commands:')
    console.log('  migrate  - Run the initial schema migration')
    console.log('  seed     - Run seed data only')
    console.log('  reset    - Drop and recreate all tables (dangerous!)')
    break
}
