import { QueryClient } from '@tanstack/react-query'

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 10, // 10 minutes (formerly cacheTime)
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors except 408, 429
        if (error?.status >= 400 && error?.status < 500 && ![408, 429].includes(error.status)) {
          return false
        }
        return failureCount < 3
      },
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: false,
    },
  },
})

// Query keys factory
export const queryKeys = {
  // Auth
  auth: {
    user: () => ['auth', 'user'] as const,
    session: () => ['auth', 'session'] as const,
  },
  
  // Profiles
  profiles: {
    all: () => ['profiles'] as const,
    byId: (id: string) => ['profiles', id] as const,
    byRole: (role: string) => ['profiles', 'role', role] as const,
  },
  
  // Garages
  garages: {
    all: () => ['garages'] as const,
    byId: (id: string) => ['garages', id] as const,
    byOwner: (ownerId: string) => ['garages', 'owner', ownerId] as const,
    active: () => ['garages', 'active'] as const,
  },
  
  // Services
  services: {
    all: () => ['services'] as const,
    byId: (id: string) => ['services', id] as const,
    byGarage: (garageId: string) => ['services', 'garage', garageId] as const,
    active: () => ['services', 'active'] as const,
  },
  
  // Vehicles
  vehicles: {
    all: () => ['vehicles'] as const,
    byId: (id: string) => ['vehicles', id] as const,
    byCustomer: (customerId: string) => ['vehicles', 'customer', customerId] as const,
  },
  
  // Orders
  orders: {
    all: () => ['orders'] as const,
    byId: (id: string) => ['orders', id] as const,
    byCustomer: (customerId: string) => ['orders', 'customer', customerId] as const,
    byGarage: (garageId: string) => ['orders', 'garage', garageId] as const,
    byStatus: (status: string) => ['orders', 'status', status] as const,
  },
  
  // Notifications
  notifications: {
    all: () => ['notifications'] as const,
    byUser: (userId: string) => ['notifications', 'user', userId] as const,
    unread: (userId: string) => ['notifications', 'user', userId, 'unread'] as const,
  },
} as const
