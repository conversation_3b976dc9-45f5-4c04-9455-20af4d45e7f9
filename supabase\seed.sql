-- Insert sample admin user (you'll need to create this user in Supabase Auth first)
-- This is just for reference - actual user creation should be done through Supabase Auth

-- Sample garages data
INSERT INTO public.garages (id, name, description, address, phone, email, is_active) VALUES
(
    '550e8400-e29b-41d4-a716-446655440001',
    'مركز الخليج للسيارات',
    'مركز متخصص في صيانة وإصلاح جميع أنواع السيارات',
    'شارع الملك فهد، الرياض، المملكة العربية السعودية',
    '+966501234567',
    '<EMAIL>',
    true
),
(
    '550e8400-e29b-41d4-a716-446655440002',
    'ورشة النجم الذهبي',
    'خدمات صيانة سريعة وموثوقة',
    'طريق الملك عبدالعزيز، جدة، المملكة العربية السعودية',
    '+966502345678',
    '<EMAIL>',
    true
),
(
    '550e8400-e29b-41d4-a716-************',
    'مركز الشرق للسيارات',
    'متخصصون في السيارات الفاخرة والرياضية',
    'شارع الأمير محمد بن فهد، الدمام، المملكة العربية السعودية',
    '+966503456789',
    '<EMAIL>',
    true
);

-- Sample services data
INSERT INTO public.services (garage_id, name, description, price, duration_minutes, is_active) VALUES
-- Gulf Auto Center services
('550e8400-e29b-41d4-a716-446655440001', 'تغيير زيت المحرك', 'تغيير زيت المحرك مع الفلتر', 150.00, 30, true),
('550e8400-e29b-41d4-a716-446655440001', 'فحص شامل للسيارة', 'فحص كامل لجميع أجزاء السيارة', 200.00, 60, true),
('550e8400-e29b-41d4-a716-446655440001', 'تغيير إطارات', 'تركيب وتوازن الإطارات الجديدة', 400.00, 45, true),
('550e8400-e29b-41d4-a716-446655440001', 'صيانة المكابح', 'فحص وتغيير أقراص وبطانات المكابح', 300.00, 90, true),
('550e8400-e29b-41d4-a716-446655440001', 'تنظيف وتلميع', 'غسيل وتنظيف شامل للسيارة', 80.00, 45, true),

-- Golden Star Workshop services
('550e8400-e29b-41d4-a716-446655440002', 'خدمة سريعة - تغيير زيت', 'تغيير زيت سريع في 15 دقيقة', 120.00, 15, true),
('550e8400-e29b-41d4-a716-446655440002', 'إصلاح كهرباء السيارة', 'تشخيص وإصلاح الأعطال الكهربائية', 250.00, 120, true),
('550e8400-e29b-41d4-a716-446655440002', 'صيانة التكييف', 'فحص وإصلاح نظام التكييف', 180.00, 60, true),
('550e8400-e29b-41d4-a716-446655440002', 'تغيير بطارية', 'تركيب بطارية جديدة مع الضمان', 350.00, 20, true),

-- East Auto Center services
('550e8400-e29b-41d4-a716-************', 'صيانة السيارات الفاخرة', 'خدمة متخصصة للسيارات الفاخرة', 500.00, 180, true),
('550e8400-e29b-41d4-a716-************', 'تشخيص بالكمبيوتر', 'فحص شامل بأحدث أجهزة التشخيص', 150.00, 30, true),
('550e8400-e29b-41d4-a716-************', 'إصلاح ناقل الحركة', 'صيانة وإصلاح ناقل الحركة', 800.00, 240, true),
('550e8400-e29b-41d4-a716-************', 'برمجة مفاتيح السيارة', 'برمجة وتكويد مفاتيح ذكية', 200.00, 30, true);

-- Note: To create admin and garage owner accounts, you need to:
-- 1. Go to Supabase Auth dashboard
-- 2. Create users with the following emails:
--    - <EMAIL> (role: admin)
--    - <EMAIL> (role: garage_owner) - for Gulf Auto Center
--    - <EMAIL> (role: garage_owner) - for Golden Star Workshop  
--    - <EMAIL> (role: garage_owner) - for East Auto Center
-- 3. Set their user metadata with the role field
-- 4. The trigger will automatically create their profiles

-- After creating the users in Supabase Auth, update the garage owner_id:
-- UPDATE public.garages SET owner_id = 'USER_ID_FROM_AUTH' WHERE email = '<EMAIL>';
-- UPDATE public.garages SET owner_id = 'USER_ID_FROM_AUTH' WHERE email = '<EMAIL>';
-- UPDATE public.garages SET owner_id = 'USER_ID_FROM_AUTH' WHERE email = '<EMAIL>';
