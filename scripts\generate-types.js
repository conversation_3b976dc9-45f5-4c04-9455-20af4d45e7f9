#!/usr/bin/env node

/**
 * Type Generation Script
 * Generates TypeScript types from Supabase schema
 */

import { exec } from 'child_process'
import { promisify } from 'util'
import fs from 'fs'
import path from 'path'

const execAsync = promisify(exec)

async function generateTypes() {
  try {
    console.log('🔄 Generating TypeScript types from Supabase schema...')
    
    // Check if supabase CLI is installed
    try {
      await execAsync('supabase --version')
    } catch (error) {
      console.error('❌ Supabase CLI not found!')
      console.error('Install it with: npm install -g supabase')
      process.exit(1)
    }
    
    // Generate types
    const { stdout, stderr } = await execAsync('supabase gen types typescript --local')
    
    if (stderr) {
      console.warn('⚠️  Warnings:', stderr)
    }
    
    // Write types to file
    const typesPath = path.join(process.cwd(), 'src/integrations/supabase/types.generated.ts')
    fs.writeFileSync(typesPath, stdout)
    
    console.log('✅ Types generated successfully!')
    console.log(`📁 Types saved to: ${typesPath}`)
    
    // Update the main types file to export generated types
    const mainTypesPath = path.join(process.cwd(), 'src/integrations/supabase/types.ts')
    const mainTypesContent = fs.readFileSync(mainTypesPath, 'utf8')
    
    if (!mainTypesContent.includes('types.generated')) {
      const exportLine = "\nexport * from './types.generated'\n"
      fs.appendFileSync(mainTypesPath, exportLine)
      console.log('✅ Updated main types file with generated exports')
    }
    
  } catch (error) {
    console.error('❌ Type generation failed:', error.message)
    
    if (error.message.includes('not linked')) {
      console.log('')
      console.log('💡 Tip: Link your project first with:')
      console.log('   supabase link --project-ref YOUR_PROJECT_ID')
    }
    
    process.exit(1)
  }
}

async function setupSupabase() {
  try {
    console.log('🔧 Setting up Supabase CLI...')
    
    // Initialize Supabase if not already done
    if (!fs.existsSync('supabase/config.toml')) {
      console.log('📦 Initializing Supabase project...')
      await execAsync('supabase init')
    }
    
    console.log('✅ Supabase setup complete!')
    console.log('')
    console.log('Next steps:')
    console.log('1. Link to your project: supabase link --project-ref YOUR_PROJECT_ID')
    console.log('2. Generate types: npm run generate-types')
    
  } catch (error) {
    console.error('❌ Supabase setup failed:', error.message)
    process.exit(1)
  }
}

// Check command line arguments
const command = process.argv[2]

switch (command) {
  case 'generate':
    generateTypes()
    break
  case 'setup':
    setupSupabase()
    break
  default:
    console.log('Usage: node generate-types.js [generate|setup]')
    console.log('')
    console.log('Commands:')
    console.log('  generate  - Generate TypeScript types from Supabase schema')
    console.log('  setup     - Initialize Supabase CLI project')
    break
}
