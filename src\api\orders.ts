import { supabase } from '@/integrations/supabase/client'
import type { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types'

export type Order = Tables<'orders'>
export type OrderInsert = TablesInsert<'orders'>
export type OrderUpdate = TablesUpdate<'orders'>
export type OrderService = Tables<'order_services'>
export type OrderServiceInsert = TablesInsert<'order_services'>

export interface OrderWithDetails extends Order {
  customer?: {
    full_name: string
    email: string
    phone: string
  }
  garage?: {
    name: string
    address: string
    phone: string
  }
  vehicle?: {
    make: string
    model: string
    year: number
    license_plate: string
  }
  order_services?: Array<{
    id: string
    quantity: number
    price: number
    service: {
      name: string
      description: string
    }
  }>
}

export class OrdersAPI {
  // Get orders by customer
  static async getOrdersByCustomer(customerId: string): Promise<OrderWithDetails[]> {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        garage:garages (
          name,
          address,
          phone
        ),
        vehicle:vehicles (
          make,
          model,
          year,
          license_plate
        ),
        order_services (
          id,
          quantity,
          price,
          service:services (
            name,
            description
          )
        )
      `)
      .eq('customer_id', customerId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  }

  // Get orders by garage
  static async getOrdersByGarage(garageId: string): Promise<OrderWithDetails[]> {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        customer:profiles!orders_customer_id_fkey (
          full_name,
          email,
          phone
        ),
        vehicle:vehicles (
          make,
          model,
          year,
          license_plate
        ),
        order_services (
          id,
          quantity,
          price,
          service:services (
            name,
            description
          )
        )
      `)
      .eq('garage_id', garageId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  }

  // Get order by ID
  static async getOrderById(id: string): Promise<OrderWithDetails | null> {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        customer:profiles!orders_customer_id_fkey (
          full_name,
          email,
          phone
        ),
        garage:garages (
          name,
          address,
          phone
        ),
        vehicle:vehicles (
          make,
          model,
          year,
          license_plate
        ),
        order_services (
          id,
          quantity,
          price,
          service:services (
            name,
            description
          )
        )
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  }

  // Create order with services
  static async createOrder(
    order: OrderInsert,
    services: Array<{ service_id: string; quantity: number; price: number }>
  ): Promise<OrderWithDetails> {
    // Start a transaction
    const { data: orderData, error: orderError } = await supabase
      .from('orders')
      .insert(order)
      .select()
      .single()

    if (orderError) throw orderError

    // Add services to the order
    if (services.length > 0) {
      const orderServices = services.map(service => ({
        order_id: orderData.id,
        service_id: service.service_id,
        quantity: service.quantity,
        price: service.price,
      }))

      const { error: servicesError } = await supabase
        .from('order_services')
        .insert(orderServices)

      if (servicesError) throw servicesError
    }

    // Return the complete order
    return this.getOrderById(orderData.id) as Promise<OrderWithDetails>
  }

  // Update order
  static async updateOrder(id: string, updates: OrderUpdate): Promise<OrderWithDetails> {
    const { data, error } = await supabase
      .from('orders')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error

    // Return the complete order
    return this.getOrderById(id) as Promise<OrderWithDetails>
  }

  // Update order status
  static async updateOrderStatus(id: string, status: string): Promise<OrderWithDetails> {
    const updates: OrderUpdate = { status: status as any }
    
    // If completing the order, set completed_date
    if (status === 'completed') {
      updates.completed_date = new Date().toISOString()
    }

    return this.updateOrder(id, updates)
  }

  // Delete order
  static async deleteOrder(id: string): Promise<void> {
    const { error } = await supabase
      .from('orders')
      .delete()
      .eq('id', id)

    if (error) throw error
  }

  // Get orders by status
  static async getOrdersByStatus(status: string): Promise<OrderWithDetails[]> {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        customer:profiles!orders_customer_id_fkey (
          full_name,
          email,
          phone
        ),
        garage:garages (
          name,
          address,
          phone
        ),
        vehicle:vehicles (
          make,
          model,
          year,
          license_plate
        ),
        order_services (
          id,
          quantity,
          price,
          service:services (
            name,
            description
          )
        )
      `)
      .eq('status', status)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  }

  // Get recent orders (for admin dashboard)
  static async getRecentOrders(limit: number = 10): Promise<OrderWithDetails[]> {
    const { data, error } = await supabase
      .from('orders')
      .select(`
        *,
        customer:profiles!orders_customer_id_fkey (
          full_name,
          email,
          phone
        ),
        garage:garages (
          name,
          address,
          phone
        ),
        vehicle:vehicles (
          make,
          model,
          year,
          license_plate
        )
      `)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data || []
  }

  // Get order statistics
  static async getOrderStats() {
    const { data, error } = await supabase
      .from('orders')
      .select('status, total_amount, created_at')

    if (error) throw error

    const stats = {
      total: data?.length || 0,
      pending: data?.filter(o => o.status === 'pending').length || 0,
      in_progress: data?.filter(o => o.status === 'in_progress').length || 0,
      completed: data?.filter(o => o.status === 'completed').length || 0,
      cancelled: data?.filter(o => o.status === 'cancelled').length || 0,
      total_revenue: data?.reduce((sum, o) => sum + (o.total_amount || 0), 0) || 0,
    }

    return stats
  }
}
