import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { StatsCard, StatsGrid } from "@/components/ui/stats-card"
import { LoadingPage } from "@/components/ui/loading-spinner"
import { EmptyState } from "@/components/ui/empty-state"
import { StatusBadge } from "@/components/ui/status-badge"
import { useAuthContext } from "@/contexts/AuthContext"
import { useGaragesByOwner } from "@/hooks/useGarages"
import { useOrdersByGarage } from "@/hooks/useOrders"
import { Car, Calendar, Clock, Plus, Wrench, FileText, Users, DollarSign } from "lucide-react"
import { Link } from "react-router-dom"

export function GarageDashboard() {
  const { user } = useAuthContext()
  
  const { data: garages, isLoading: garagesLoading } = useGaragesByOwner(user?.id || '')
  const garage = garages?.[0] // Assuming one garage per owner for now
  
  const { data: orders, isLoading: ordersLoading } = useOrdersByGarage(garage?.id || '')

  if (garagesLoading || ordersLoading) {
    return <LoadingPage text="جاري تحميل لوحة التحكم..." />
  }

  if (!garage) {
    return (
      <div className="p-6">
        <EmptyState
          icon={Wrench}
          title="لا توجد ورشة مسجلة"
          description="يجب أن يكون لديك ورشة مسجلة للوصول إلى لوحة التحكم"
          action={{
            label: "تواصل مع الإدارة",
            onClick: () => window.location.href = "/support"
          }}
        />
      </div>
    )
  }

  const pendingOrders = orders?.filter(o => o.status === 'pending').length || 0
  const inProgressOrders = orders?.filter(o => o.status === 'in_progress').length || 0
  const completedOrders = orders?.filter(o => o.status === 'completed').length || 0
  const totalRevenue = orders?.reduce((sum, o) => sum + (o.total_amount || 0), 0) || 0

  const recentOrders = orders?.slice(0, 5) || []
  const uniqueCustomers = new Set(orders?.map(o => o.customer_id)).size

  return (
    <div className="space-y-6 p-6">
      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">مرحباً، {user?.full_name || 'مالك الورشة'}</h1>
          <p className="text-muted-foreground">
            إدارة ورشة {garage.name}
          </p>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link to="/garage/services">
              <Plus className="ml-2 h-4 w-4" />
              إضافة خدمة
            </Link>
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <StatsGrid>
        <StatsCard
          title="الطلبات المعلقة"
          value={pendingOrders}
          description="تحتاج للمراجعة"
          icon={Clock}
        />
        <StatsCard
          title="قيد التنفيذ"
          value={inProgressOrders}
          description="طلبات جاري العمل عليها"
          icon={Wrench}
        />
        <StatsCard
          title="العملاء"
          value={uniqueCustomers}
          description="عملاء مختلفون"
          icon={Users}
        />
        <StatsCard
          title="الإيرادات"
          value={`${totalRevenue.toLocaleString()} ر.س`}
          description="إجمالي الإيرادات"
          icon={DollarSign}
        />
      </StatsGrid>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Recent Orders */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              الطلبات الأخيرة
            </CardTitle>
            <CardDescription>
              آخر 5 طلبات واردة
            </CardDescription>
          </CardHeader>
          <CardContent>
            {recentOrders.length > 0 ? (
              <div className="space-y-4">
                {recentOrders.map((order) => (
                  <div key={order.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="space-y-1">
                      <p className="font-medium">{order.customer?.full_name}</p>
                      <p className="text-sm text-muted-foreground">
                        {order.vehicle?.make} {order.vehicle?.model} - {order.vehicle?.license_plate}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(order.created_at).toLocaleDateString('ar-SA')}
                      </p>
                    </div>
                    <div className="text-left">
                      <StatusBadge status={order.status} />
                      {order.total_amount && (
                        <p className="text-sm font-medium mt-1">
                          {order.total_amount} ر.س
                        </p>
                      )}
                    </div>
                  </div>
                ))}
                <Button variant="outline" className="w-full" asChild>
                  <Link to="/garage/orders">عرض جميع الطلبات</Link>
                </Button>
              </div>
            ) : (
              <EmptyState
                icon={Calendar}
                title="لا توجد طلبات"
                description="لم تستلم أي طلبات صيانة بعد"
              />
            )}
          </CardContent>
        </Card>

        {/* Garage Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wrench className="h-5 w-5" />
              معلومات الورشة
            </CardTitle>
            <CardDescription>
              تفاصيل ورشتك
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <p className="font-medium">{garage.name}</p>
                {garage.description && (
                  <p className="text-sm text-muted-foreground">{garage.description}</p>
                )}
              </div>
              
              {garage.address && (
                <div>
                  <p className="text-sm font-medium">العنوان:</p>
                  <p className="text-sm text-muted-foreground">{garage.address}</p>
                </div>
              )}
              
              {garage.phone && (
                <div>
                  <p className="text-sm font-medium">الهاتف:</p>
                  <p className="text-sm text-muted-foreground">{garage.phone}</p>
                </div>
              )}
              
              <div className="flex items-center gap-2">
                <p className="text-sm font-medium">الحالة:</p>
                <StatusBadge status={garage.is_active ? 'active' : 'inactive'} />
              </div>
              
              <Button variant="outline" className="w-full" asChild>
                <Link to="/garage/settings">تحديث معلومات الورشة</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>الإجراءات السريعة</CardTitle>
          <CardDescription>
            الوصول السريع للخدمات الأكثر استخداماً
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            <Button variant="outline" className="h-20 flex-col gap-2" asChild>
              <Link to="/garage/orders">
                <Calendar className="h-6 w-6" />
                إدارة الطلبات
              </Link>
            </Button>
            <Button variant="outline" className="h-20 flex-col gap-2" asChild>
              <Link to="/garage/services">
                <Wrench className="h-6 w-6" />
                إدارة الخدمات
              </Link>
            </Button>
            <Button variant="outline" className="h-20 flex-col gap-2" asChild>
              <Link to="/garage/customers">
                <Users className="h-6 w-6" />
                العملاء
              </Link>
            </Button>
            <Button variant="outline" className="h-20 flex-col gap-2" asChild>
              <Link to="/garage/reports">
                <FileText className="h-6 w-6" />
                التقارير
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
