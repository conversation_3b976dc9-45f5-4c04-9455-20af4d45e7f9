import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { AuthAPI, type SignUpData, type SignInData, type ResetPasswordData, type UpdatePasswordData } from '@/api/auth'
import { queryKeys } from '@/lib/react-query'
import type { AuthUser } from '@/integrations/supabase/types'
import { toast } from 'sonner'

export function useAuth() {
  const queryClient = useQueryClient()
  const navigate = useNavigate()
  const [isInitialized, setIsInitialized] = useState(false)

  // Get current user query
  const {
    data: user,
    isLoading,
    error,
  } = useQuery({
    queryKey: queryKeys.auth.user(),
    queryFn: AuthAPI.getCurrentUser,
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: false,
  })

  // Get current session query
  const { data: session } = useQuery({
    queryKey: queryKeys.auth.session(),
    queryFn: AuthAPI.getSession,
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: false,
  })

  // Sign up mutation
  const signUpMutation = useMutation({
    mutationFn: (data: SignUpData) => AuthAPI.signUp(data),
    onSuccess: (result) => {
      if (result.error) {
        toast.error(result.error)
      } else {
        toast.success('تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني')
        navigate('/auth/verify-email', { 
          state: { email: result.user?.email } 
        })
      }
    },
    onError: () => {
      toast.error('حدث خطأ غير متوقع')
    },
  })

  // Sign in mutation
  const signInMutation = useMutation({
    mutationFn: (data: SignInData) => AuthAPI.signIn(data),
    onSuccess: (result) => {
      if (result.error) {
        toast.error(result.error)
      } else if (result.user) {
        queryClient.setQueryData(queryKeys.auth.user(), result.user)
        toast.success('مرحباً بك!')
        
        // Redirect based on role
        switch (result.user.role) {
          case 'admin':
            navigate('/admin')
            break
          case 'garage_owner':
            navigate('/garage')
            break
          case 'customer':
            navigate('/customer')
            break
          default:
            navigate('/')
        }
      }
    },
    onError: () => {
      toast.error('حدث خطأ غير متوقع')
    },
  })

  // Google sign in mutation
  const googleSignInMutation = useMutation({
    mutationFn: () => AuthAPI.signInWithGoogle(),
    onSuccess: (result) => {
      if (result.error) {
        toast.error(result.error)
      }
      // Success will be handled by the auth state change listener
    },
    onError: () => {
      toast.error('حدث خطأ غير متوقع')
    },
  })

  // Sign out mutation
  const signOutMutation = useMutation({
    mutationFn: () => AuthAPI.signOut(),
    onSuccess: (result) => {
      if (result.error) {
        toast.error(result.error)
      } else {
        queryClient.clear()
        toast.success('تم تسجيل الخروج بنجاح')
        navigate('/auth/login')
      }
    },
    onError: () => {
      toast.error('حدث خطأ غير متوقع')
    },
  })

  // Reset password mutation
  const resetPasswordMutation = useMutation({
    mutationFn: (data: ResetPasswordData) => AuthAPI.resetPassword(data),
    onSuccess: (result) => {
      if (result.error) {
        toast.error(result.error)
      } else {
        toast.success('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني')
        navigate('/auth/login')
      }
    },
    onError: () => {
      toast.error('حدث خطأ غير متوقع')
    },
  })

  // Update password mutation
  const updatePasswordMutation = useMutation({
    mutationFn: (data: UpdatePasswordData) => AuthAPI.updatePassword(data),
    onSuccess: (result) => {
      if (result.error) {
        toast.error(result.error)
      } else {
        toast.success('تم تحديث كلمة المرور بنجاح')
        navigate('/auth/login')
      }
    },
    onError: () => {
      toast.error('حدث خطأ غير متوقع')
    },
  })

  // Verify OTP mutation
  const verifyOtpMutation = useMutation({
    mutationFn: ({ email, token }: { email: string; token: string }) => 
      AuthAPI.verifyOtp(email, token),
    onSuccess: (result) => {
      if (result.error) {
        toast.error(result.error)
      } else {
        toast.success('تم التحقق من البريد الإلكتروني بنجاح')
        queryClient.invalidateQueries({ queryKey: queryKeys.auth.user() })
        navigate('/customer')
      }
    },
    onError: () => {
      toast.error('حدث خطأ غير متوقع')
    },
  })

  // Resend verification mutation
  const resendVerificationMutation = useMutation({
    mutationFn: (email: string) => AuthAPI.resendVerification(email),
    onSuccess: (result) => {
      if (result.error) {
        toast.error(result.error)
      } else {
        toast.success('تم إعادة إرسال رمز التحقق')
      }
    },
    onError: () => {
      toast.error('حدث خطأ غير متوقع')
    },
  })

  // Listen to auth state changes
  useEffect(() => {
    const { data: { subscription } } = AuthAPI.onAuthStateChange((user) => {
      queryClient.setQueryData(queryKeys.auth.user(), user)
      setIsInitialized(true)
    })

    return () => subscription.unsubscribe()
  }, [queryClient])

  // Helper functions
  const isAuthenticated = !!user && !!session
  const isAdmin = user?.role === 'admin'
  const isGarageOwner = user?.role === 'garage_owner'
  const isCustomer = user?.role === 'customer'

  return {
    // State
    user,
    session,
    isLoading: isLoading || !isInitialized,
    isAuthenticated,
    isAdmin,
    isGarageOwner,
    isCustomer,
    error,

    // Actions
    signUp: signUpMutation.mutate,
    signIn: signInMutation.mutate,
    signInWithGoogle: googleSignInMutation.mutate,
    signOut: signOutMutation.mutate,
    resetPassword: resetPasswordMutation.mutate,
    updatePassword: updatePasswordMutation.mutate,
    verifyOtp: verifyOtpMutation.mutate,
    resendVerification: resendVerificationMutation.mutate,

    // Loading states
    isSigningUp: signUpMutation.isPending,
    isSigningIn: signInMutation.isPending,
    isSigningOut: signOutMutation.isPending,
    isResettingPassword: resetPasswordMutation.isPending,
    isUpdatingPassword: updatePasswordMutation.isPending,
    isVerifyingOtp: verifyOtpMutation.isPending,
    isResendingVerification: resendVerificationMutation.isPending,
  }
}
