import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuthContext } from '@/contexts/AuthContext'
import { supabase } from '@/integrations/supabase/client'
import { Loader2 } from 'lucide-react'
import { toast } from 'sonner'

export function AuthCallback() {
  const navigate = useNavigate()
  const { user } = useAuthContext()

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        const { data, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('Auth callback error:', error)
          toast.error('حدث خطأ في تسجيل الدخول')
          navigate('/auth/login')
          return
        }

        if (data.session?.user) {
          // Check if user has a profile
          const { data: profile, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', data.session.user.id)
            .single()

          if (profileError || !profile) {
            // Create profile for Google OAuth users
            const { error: insertError } = await supabase
              .from('profiles')
              .insert({
                id: data.session.user.id,
                email: data.session.user.email!,
                full_name: data.session.user.user_metadata?.full_name || 
                          data.session.user.user_metadata?.name || '',
                role: 'customer', // Google OAuth users are always customers
                avatar_url: data.session.user.user_metadata?.avatar_url,
              })

            if (insertError) {
              console.error('Profile creation error:', insertError)
              toast.error('حدث خطأ في إنشاء الملف الشخصي')
              navigate('/auth/login')
              return
            }
          }

          toast.success('تم تسجيل الدخول بنجاح!')
          
          // Redirect based on role (Google users are always customers)
          navigate('/customer')
        } else {
          navigate('/auth/login')
        }
      } catch (error) {
        console.error('Unexpected error:', error)
        toast.error('حدث خطأ غير متوقع')
        navigate('/auth/login')
      }
    }

    handleAuthCallback()
  }, [navigate])

  // If user is already loaded, redirect immediately
  useEffect(() => {
    if (user) {
      switch (user.role) {
        case 'admin':
          navigate('/admin')
          break
        case 'garage_owner':
          navigate('/garage')
          break
        case 'customer':
          navigate('/customer')
          break
        default:
          navigate('/')
      }
    }
  }, [user, navigate])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
        <h2 className="text-xl font-semibold mb-2">جاري تسجيل الدخول...</h2>
        <p className="text-muted-foreground">يرجى الانتظار بينما نقوم بتسجيل دخولك</p>
      </div>
    </div>
  )
}
