import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import {
  Search,
  Filter,
  MessageCircle,
  Clock,
  User,
  Building2,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Send,
  Eye,
  Calendar,
  FileText,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Mock support tickets data
const mockTickets = [
  {
    id: "T001",
    ticketNumber: "SUP-2024-001",
    subject: "مشكلة في تسجيل الدخول",
    message: "لا أستطيع تسجيل الدخول إلى حسابي منذ يومين. يظهر لي خطأ في كلمة المرور رغم أنها صحيحة.",
    status: "open",
    priority: "high",
    user: {
      name: "أحمد محمد",
      type: "customer",
      email: "<EMAIL>",
      phone: "+966501234567"
    },
    createdAt: "2024-01-15T10:30:00",
    updatedAt: "2024-01-15T10:30:00",
    adminReply: null,
    category: "technical"
  },
  {
    id: "T002",
    ticketNumber: "SUP-2024-002", 
    subject: "طلب استرداد مبلغ",
    message: "تم خصم مبلغ 150 ريال من حسابي دون إتمام الخدمة. أريد استرداد المبلغ.",
    status: "in_progress",
    priority: "medium",
    user: {
      name: "سارة أحمد", 
      type: "customer",
      email: "<EMAIL>",
      phone: "+966501234568"
    },
    createdAt: "2024-01-14T14:20:00",
    updatedAt: "2024-01-15T09:15:00",
    adminReply: "تم التحقق من المعاملة، سيتم الاسترداد خلال 3-5 أيام عمل.",
    category: "financial"
  },
  {
    id: "T003",
    ticketNumber: "SUP-2024-003",
    subject: "مشكلة في التحقق من الكراج",
    message: "تم رفض طلب التحقق من كراجي دون توضيح الأسباب. أريد معرفة المطلوب للموافقة.",
    status: "closed",
    priority: "low",
    user: {
      name: "كراج النور",
      type: "garage",
      email: "<EMAIL>", 
      phone: "+966501234569"
    },
    createdAt: "2024-01-12T11:45:00",
    updatedAt: "2024-01-14T16:30:00",
    adminReply: "تم رفض الطلب بسبب عدم اكتمال المستندات المطلوبة. يرجى رفع الرخصة التجارية وشهادة السجل التجاري.",
    category: "verification"
  },
  {
    id: "T004",
    ticketNumber: "SUP-2024-004",
    subject: "شكوى من جودة الخدمة",
    message: "خدمة سيئة جداً في كراج الشرق. تم تأخير العمل 3 أيام وجودة العمل رديئة.",
    status: "open",
    priority: "high",
    user: {
      name: "محمد علي",
      type: "customer", 
      email: "<EMAIL>",
      phone: "+966501234570"
    },
    createdAt: "2024-01-15T08:15:00",
    updatedAt: "2024-01-15T08:15:00",
    adminReply: null,
    category: "complaint"
  },
];

const statusOptions = [
  { value: "all", label: "جميع التذاكر", color: "default" },
  { value: "open", label: "مفتوحة", color: "destructive" },
  { value: "in_progress", label: "قيد المعالجة", color: "secondary" },
  { value: "closed", label: "مغلقة", color: "default" },
];

const priorityOptions = [
  { value: "all", label: "جميع الأولويات" },
  { value: "low", label: "منخفضة" },
  { value: "medium", label: "متوسطة" },
  { value: "high", label: "عالية" },
];

const categoryOptions = [
  { value: "all", label: "جميع الفئات" },
  { value: "technical", label: "تقنية" },
  { value: "financial", label: "مالية" },
  { value: "verification", label: "التحقق" },
  { value: "complaint", label: "شكاوى" },
];

const getStatusBadge = (status: string) => {
  switch (status) {
    case "open":
      return <Badge variant="destructive" className="font-arabic">مفتوحة</Badge>;
    case "in_progress":
      return <Badge variant="secondary" className="font-arabic">قيد المعالجة</Badge>;
    case "closed":
      return <Badge className="bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 font-arabic">مغلقة</Badge>;
    default:
      return <Badge variant="outline" className="font-arabic">{status}</Badge>;
  }
};

const getPriorityBadge = (priority: string) => {
  switch (priority) {
    case "high":
      return <Badge className="bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300 font-arabic">عالية</Badge>;
    case "medium":
      return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300 font-arabic">متوسطة</Badge>;
    case "low":
      return <Badge className="bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 font-arabic">منخفضة</Badge>;
    default:
      return <Badge variant="outline" className="font-arabic">{priority}</Badge>;
  }
};

export default function SupportTickets() {
  const [tickets, setTickets] = useState(mockTickets);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [priorityFilter, setPriorityFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [replyMessage, setReplyMessage] = useState("");
  const { toast } = useToast();

  const filteredTickets = tickets.filter(ticket => {
    const matchesSearch = 
      ticket.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ticket.ticketNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
      ticket.user.name.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || ticket.status === statusFilter;
    const matchesPriority = priorityFilter === "all" || ticket.priority === priorityFilter;
    const matchesCategory = categoryFilter === "all" || ticket.category === categoryFilter;
    
    return matchesSearch && matchesStatus && matchesPriority && matchesCategory;
  });

  const handleReply = (ticketId: string) => {
    if (!replyMessage.trim()) {
      toast({
        title: "خطأ",
        description: "يرجى كتابة رد قبل الإرسال",
        variant: "destructive"
      });
      return;
    }

    setTickets(tickets.map(ticket => 
      ticket.id === ticketId 
        ? { 
            ...ticket, 
            adminReply: replyMessage,
            status: "in_progress",
            updatedAt: new Date().toISOString()
          }
        : ticket
    ));

    setReplyMessage("");
    toast({
      title: "تم إرسال الرد",
      description: "تم إرسال الرد للعميل بنجاح",
    });
  };

  const handleStatusChange = (ticketId: string, newStatus: string) => {
    setTickets(tickets.map(ticket => 
      ticket.id === ticketId 
        ? { ...ticket, status: newStatus, updatedAt: new Date().toISOString() }
        : ticket
    ));

    toast({
      title: "تم تحديث الحالة",
      description: `تم تغيير حالة التذكرة إلى ${newStatus === "closed" ? "مغلقة" : newStatus === "in_progress" ? "قيد المعالجة" : "مفتوحة"}`,
    });
  };

  const getTimeAgo = (date: string) => {
    const now = new Date();
    const ticketDate = new Date(date);
    const diffInHours = Math.floor((now.getTime() - ticketDate.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return "منذ قليل";
    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;
    return `منذ ${Math.floor(diffInHours / 24)} يوم`;
  };

  const openTicketsCount = tickets.filter(t => t.status === "open").length;
  const inProgressTicketsCount = tickets.filter(t => t.status === "in_progress").length;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground font-arabic">الدعم الفني</h1>
          <p className="text-muted-foreground font-arabic">
            إدارة طلبات الدعم والشكاوى ({openTicketsCount} مفتوحة، {inProgressTicketsCount} قيد المعالجة)
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-br from-red-50 to-red-100/50 dark:from-red-900/20 dark:to-red-800/10 border-red-200 dark:border-red-800/30">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-red-700 dark:text-red-300 font-arabic">تذاكر مفتوحة</p>
                <p className="text-2xl font-bold text-red-800 dark:text-red-200">{openTicketsCount}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100/50 dark:from-yellow-900/20 dark:to-yellow-800/10 border-yellow-200 dark:border-yellow-800/30">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-yellow-700 dark:text-yellow-300 font-arabic">قيد المعالجة</p>
                <p className="text-2xl font-bold text-yellow-800 dark:text-yellow-200">{inProgressTicketsCount}</p>
              </div>
              <Clock className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100/50 dark:from-green-900/20 dark:to-green-800/10 border-green-200 dark:border-green-800/30">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-green-700 dark:text-green-300 font-arabic">تذاكر مغلقة</p>
                <p className="text-2xl font-bold text-green-800 dark:text-green-200">
                  {tickets.filter(t => t.status === "closed").length}
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-900/20 dark:to-blue-800/10 border-blue-200 dark:border-blue-800/30">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-700 dark:text-blue-300 font-arabic">إجمالي التذاكر</p>
                <p className="text-2xl font-bold text-blue-800 dark:text-blue-200">{tickets.length}</p>
              </div>
              <FileText className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4 items-center">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="البحث في التذاكر..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pr-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {priorityOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {categoryOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Tickets List */}
      <div className="space-y-4">
        {filteredTickets.length === 0 ? (
          <Card className="p-8 text-center">
            <MessageCircle className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2 font-arabic">لا توجد تذاكر</h3>
            <p className="text-muted-foreground font-arabic">لم يتم العثور على تذاكر تطابق المعايير المحددة</p>
          </Card>
        ) : (
          filteredTickets.map((ticket) => (
            <Card key={ticket.id} className="hover:shadow-md transition-all duration-200">
              <CardContent className="p-6">
                <div className="space-y-4">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <h3 className="text-lg font-semibold font-arabic">{ticket.subject}</h3>
                        <Badge variant="outline" className="font-arabic">{ticket.ticketNumber}</Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span className="flex items-center gap-1 font-arabic">
                          {ticket.user.type === "garage" ? <Building2 className="w-4 h-4" /> : <User className="w-4 h-4" />}
                          {ticket.user.name}
                        </span>
                        <span className="flex items-center gap-1 font-arabic">
                          <Calendar className="w-4 h-4" />
                          {getTimeAgo(ticket.createdAt)}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(ticket.status)}
                      {getPriorityBadge(ticket.priority)}
                    </div>
                  </div>

                  {/* Message */}
                  <div className="bg-muted/30 rounded-lg p-4">
                    <p className="text-foreground font-arabic">{ticket.message}</p>
                  </div>

                  {/* Admin Reply */}
                  {ticket.adminReply && (
                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border-r-4 border-blue-500">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge className="bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 font-arabic">
                          رد الإدارة
                        </Badge>
                      </div>
                      <p className="text-foreground font-arabic">{ticket.adminReply}</p>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex items-center justify-between pt-4 border-t">
                    <div className="flex items-center gap-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm" className="font-arabic">
                            <Eye className="w-4 h-4 ml-1" />
                            عرض التفاصيل
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle className="font-arabic">تفاصيل التذكرة {ticket.ticketNumber}</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label className="font-arabic">اسم المستخدم</Label>
                                <p className="text-sm">{ticket.user.name}</p>
                              </div>
                              <div>
                                <Label className="font-arabic">البريد الإلكتروني</Label>
                                <p className="text-sm">{ticket.user.email}</p>
                              </div>
                              <div>
                                <Label className="font-arabic">رقم الهاتف</Label>
                                <p className="text-sm">{ticket.user.phone}</p>
                              </div>
                              <div>
                                <Label className="font-arabic">نوع المستخدم</Label>
                                <p className="text-sm font-arabic">{ticket.user.type === "garage" ? "صاحب كراج" : "عميل"}</p>
                              </div>
                            </div>
                            <div>
                              <Label className="font-arabic">الرسالة الكاملة</Label>
                              <div className="bg-muted/30 rounded-lg p-4 mt-2">
                                <p className="font-arabic">{ticket.message}</p>
                              </div>
                            </div>
                            {!ticket.adminReply && (
                              <div>
                                <Label className="font-arabic">رد الإدارة</Label>
                                <Textarea
                                  value={replyMessage}
                                  onChange={(e) => setReplyMessage(e.target.value)}
                                  placeholder="اكتب ردك هنا..."
                                  className="mt-2"
                                  rows={4}
                                />
                                <Button
                                  onClick={() => handleReply(ticket.id)}
                                  className="mt-2 font-arabic"
                                >
                                  <Send className="w-4 h-4 ml-2" />
                                  إرسال الرد
                                </Button>
                              </div>
                            )}
                          </div>
                        </DialogContent>
                      </Dialog>
                      
                      {ticket.status !== "closed" && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleStatusChange(ticket.id, "closed")}
                          className="font-arabic"
                        >
                          <CheckCircle className="w-4 h-4 ml-1" />
                          إغلاق التذكرة
                        </Button>
                      )}
                    </div>

                    <div className="text-sm text-muted-foreground font-arabic">
                      آخر تحديث: {getTimeAgo(ticket.updatedAt)}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}