import { Eye, Download, Calendar, Car } from "lucide-react";
import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

interface InvoiceDetailsModalProps {
  children: React.ReactNode;
  invoice?: {
    number: string;
    date: string;
    service: string;
    total: number;
    paid: number;
    remaining: number;
    status: string;
    garage?: string;
    vehicle?: string;
  };
}

export function InvoiceDetailsModal({ children, invoice }: InvoiceDetailsModalProps) {
  const mockInvoice = {
    number: "INV-2024-001",
    date: "2024-01-15",
    service: "صيانة دورية شاملة",
    total: 850,
    paid: 850,
    remaining: 0,
    status: "مدفوع",
    garage: "كراج الخليج المتطور",
    vehicle: "تويوتا كامري 2022 - أ ب ج 1234",
    items: [
      { name: "تغيير زيت المحرك", price: 200, quantity: 1 },
      { name: "فلتر الهواء", price: 150, quantity: 1 },
      { name: "فحص الفرامل", price: 100, quantity: 1 },
      { name: "العمالة", price: 400, quantity: 1 }
    ],
    ...invoice
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-almarai flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary-dark rounded-lg flex items-center justify-center">
              <Eye className="w-4 h-4 text-primary-foreground" />
            </div>
            تفاصيل الفاتورة
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Header */}
          <Card className="premium-card p-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-2xl font-almarai font-bold">{mockInvoice.number}</h3>
                <p className="text-muted-foreground font-arabic">{new Date(mockInvoice.date).toLocaleDateString('ar-SA')}</p>
              </div>
              <Badge className="bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400">
                {mockInvoice.status}
              </Badge>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-arabic font-medium text-muted-foreground">الكراج</h4>
                <p className="font-arabic">{mockInvoice.garage}</p>
              </div>
              <div>
                <h4 className="font-arabic font-medium text-muted-foreground">المركبة</h4>
                <p className="font-arabic">{mockInvoice.vehicle}</p>
              </div>
            </div>
          </Card>

          {/* Items */}
          <Card className="premium-card p-6">
            <h3 className="font-arabic font-semibold mb-4">تفاصيل الخدمات</h3>
            <div className="space-y-3">
              {mockInvoice.items.map((item, index) => (
                <div key={index} className="flex justify-between items-center py-2 border-b border-border/50 last:border-0">
                  <div className="flex-1">
                    <p className="font-arabic font-medium">{item.name}</p>
                    <p className="text-sm text-muted-foreground">الكمية: {item.quantity}</p>
                  </div>
                  <p className="font-medium">{item.price.toLocaleString()} ر.س</p>
                </div>
              ))}
            </div>
            
            <Separator className="my-4" />
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="font-arabic">المجموع الفرعي:</span>
                <span>{(mockInvoice.total - 50).toLocaleString()} ر.س</span>
              </div>
              <div className="flex justify-between">
                <span className="font-arabic">ضريبة القيمة المضافة (15%):</span>
                <span>50 ر.س</span>
              </div>
              <div className="flex justify-between text-lg font-bold">
                <span className="font-arabic">الإجمالي:</span>
                <span>{mockInvoice.total.toLocaleString()} ر.س</span>
              </div>
            </div>
          </Card>

          {/* Payment Status */}
          <Card className="premium-card p-6">
            <h3 className="font-arabic font-semibold mb-4">حالة الدفع</h3>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-2xl font-bold text-green-600">{mockInvoice.paid.toLocaleString()}</p>
                <p className="text-sm font-arabic text-muted-foreground">المدفوع</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-orange-600">{mockInvoice.remaining.toLocaleString()}</p>
                <p className="text-sm font-arabic text-muted-foreground">المتبقي</p>
              </div>
              <div>
                <p className="text-2xl font-bold">{mockInvoice.total.toLocaleString()}</p>
                <p className="text-sm font-arabic text-muted-foreground">الإجمالي</p>
              </div>
            </div>
          </Card>

          {/* Actions */}
          <div className="flex gap-3">
            <Button className="flex-1 btn-premium text-white font-arabic gap-2">
              <Download className="w-4 h-4" />
              تحميل PDF
            </Button>
            <Button variant="outline" className="font-arabic gap-2">
              <Car className="w-4 h-4" />
              طباعة
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}