import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
} from "recharts";
import {
  Users,
  Building2,
  ShoppingBag,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Eye,
  Calendar,
} from "lucide-react";

// Mock data
const statsData = [
  {
    title: "إجمالي المستخدمين",
    value: "12,547",
    change: "+15.3%",
    changeType: "positive" as const,
    icon: Users,
    gradient: "blue" as const,
  },
  {
    title: "أصحاب الكراجات",
    value: "1,234",
    change: "****%",
    changeType: "positive" as const,
    icon: Building2,
    gradient: "green" as const,
  },
  {
    title: "إجمالي الطلبات",
    value: "45,892",
    change: "+23.1%",
    changeType: "positive" as const,
    icon: ShoppingBag,
    gradient: "orange" as const,
  },
  {
    title: "إجمالي الإيرادات",
    value: "2,547,890 ريال",
    change: "+18.5%",
    changeType: "positive" as const,
    icon: DollarSign,
    gradient: "purple" as const,
  },
];

const monthlyData = [
  { month: "يناير", users: 1200, garages: 45, orders: 2400, revenue: 180000 },
  { month: "فبراير", users: 1350, garages: 52, orders: 2780, revenue: 205000 },
  { month: "مارس", users: 1580, garages: 68, orders: 3200, revenue: 235000 },
  { month: "أبريل", users: 1720, garages: 75, orders: 3650, revenue: 268000 },
  { month: "مايو", users: 1950, garages: 89, orders: 4100, revenue: 295000 },
  { month: "يونيو", users: 2180, garages: 98, orders: 4580, revenue: 325000 },
];

const topServices = [
  { name: "تغيير الزيت", orders: 8540, percentage: 28.5 },
  { name: "فحص شامل", orders: 6420, percentage: 21.4 },
  { name: "إصلاح الفرامل", orders: 4380, percentage: 14.6 },
  { name: "صيانة التكييف", orders: 3210, percentage: 10.7 },
  { name: "تبديل الإطارات", orders: 2890, percentage: 9.6 },
  { name: "أخرى", orders: 4560, percentage: 15.2 },
];

const orderDistribution = [
  { name: "الرياض", value: 35.2, color: "#3b82f6" },
  { name: "جدة", value: 28.7, color: "#10b981" },
  { name: "الدمام", value: 18.4, color: "#f59e0b" },
  { name: "مكة", value: 12.1, color: "#ef4444" },
  { name: "المدينة", value: 5.6, color: "#8b5cf6" },
];

const gradientClasses = {
  blue: "from-blue-500/20 to-blue-600/5 border-blue-500/20",
  green: "from-green-500/20 to-green-600/5 border-green-500/20",
  orange: "from-orange-500/20 to-orange-600/5 border-orange-500/20",
  purple: "from-purple-500/20 to-purple-600/5 border-purple-500/20",
};

const iconBgClasses = {
  blue: "bg-blue-500/10 text-blue-600",
  green: "bg-green-500/10 text-green-600",
  orange: "bg-orange-500/10 text-orange-600",
  purple: "bg-purple-500/10 text-purple-600",
};

function StatsCard({ title, value, change, changeType, icon: Icon, gradient }: any) {
  return (
    <Card className={`bg-gradient-to-br ${gradientClasses[gradient]} border backdrop-blur-sm hover:shadow-lg transition-all duration-300`}>
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${iconBgClasses[gradient]}`}>
            <Icon className="w-6 h-6" />
          </div>
          <div className={`flex items-center gap-1 text-sm font-medium ${
            changeType === "positive" ? "text-green-600" : "text-red-500"
          }`}>
            {changeType === "positive" ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
            {change}
          </div>
        </div>
        <div>
          <h3 className="text-sm font-medium text-muted-foreground mb-1">{title}</h3>
          <p className="text-2xl font-bold text-foreground">{value}</p>
        </div>
      </CardContent>
    </Card>
  );
}

export default function AdminDashboard() {
  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight text-foreground">لوحة تحكم الإدارة</h1>
        <p className="text-muted-foreground">نظرة شاملة على أداء المنصة والإحصائيات الرئيسية</p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {statsData.map((stat, index) => (
          <StatsCard key={index} {...stat} />
        ))}
      </div>

      {/* Charts Row 1 */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Monthly Growth */}
        <Card className="bg-card/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              النمو الشهري
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={monthlyData}>
                  <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                  <XAxis dataKey="month" className="text-xs" />
                  <YAxis className="text-xs" />
                  <Tooltip 
                    contentStyle={{
                      backgroundColor: 'hsl(var(--card))',
                      border: '1px solid hsl(var(--border))',
                      borderRadius: '8px'
                    }}
                  />
                  <Legend />
                  <Area 
                    type="monotone" 
                    dataKey="users" 
                    name="المستخدمين"
                    stroke="#3b82f6" 
                    fill="#3b82f6" 
                    fillOpacity={0.3}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="garages" 
                    name="الكراجات"
                    stroke="#10b981" 
                    fill="#10b981" 
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Revenue Chart */}
        <Card className="bg-card/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="w-5 h-5" />
              الإيرادات الشهرية
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={monthlyData}>
                  <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                  <XAxis dataKey="month" className="text-xs" />
                  <YAxis className="text-xs" />
                  <Tooltip 
                    contentStyle={{
                      backgroundColor: 'hsl(var(--card))',
                      border: '1px solid hsl(var(--border))',
                      borderRadius: '8px'
                    }}
                    formatter={(value) => [`${value?.toLocaleString()} ريال`, 'الإيرادات']}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="revenue" 
                    stroke="#8b5cf6" 
                    strokeWidth={3}
                    dot={{ fill: '#8b5cf6', strokeWidth: 2, r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row 2 */}
      <div className="grid gap-6 md:grid-cols-3">
        {/* Top Services */}
        <Card className="md:col-span-2 bg-card/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="w-5 h-5" />
              الخدمات الأكثر طلباً
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topServices.map((service, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-primary font-bold text-sm">
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-medium">{service.name}</p>
                      <p className="text-sm text-muted-foreground">{service.orders.toLocaleString()} طلب</p>
                    </div>
                  </div>
                  <Badge variant="secondary">{service.percentage}%</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Order Distribution */}
        <Card className="bg-card/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              توزيع الطلبات بالمدن
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={orderDistribution}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={80}
                    paddingAngle={2}
                    dataKey="value"
                  >
                    {orderDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip 
                    contentStyle={{
                      backgroundColor: 'hsl(var(--card))',
                      border: '1px solid hsl(var(--border))',
                      borderRadius: '8px'
                    }}
                    formatter={(value) => [`${value}%`, 'النسبة']}
                  />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}