import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@/components/theme-provider";
import { QueryProvider } from "@/providers/QueryProvider";
import { AuthProvider } from "@/contexts/AuthContext";
import { NotificationProvider } from "@/contexts/NotificationContext";
import { ProtectedRoute, AdminRoute, GarageOwnerRoute, CustomerRoute, PublicRoute } from "@/components/auth/ProtectedRoute";
// Auth pages
import { Login } from "./pages/auth/Login";
import { Signup } from "./pages/auth/Signup";
import { ForgotPassword } from "./pages/auth/ForgotPassword";
import { VerifyEmail } from "./pages/auth/VerifyEmail";
import { ResetPassword } from "./pages/auth/ResetPassword";
import { AuthCallback } from "./pages/auth/AuthCallback";

// Layout components
import { AppSidebar } from "./components/layout/AppSidebar";
import { Header } from "./components/layout/Header";
import { AdminLayout } from "./components/layout/AdminLayout";

// Customer pages
import Dashboard from "./pages/Dashboard";
import Garages from "./pages/Garages";
import GarageDetails from "./pages/GarageDetails";
import Vehicles from "./pages/Vehicles";
import VehicleDetails from "./pages/VehicleDetails";
import Upload from "./pages/Upload";
import Invoices from "./pages/Invoices";
import Reports from "./pages/Reports";
import CustomerNotifications from "./pages/CustomerNotifications";
import Settings from "./pages/Settings";

// Garage owner pages
import GarageDashboard from "./pages/GarageDashboard";
import GarageOrders from "./pages/garage/Orders";
import GarageServices from "./pages/garage/Services";
import GarageVehicles from "./pages/garage/Vehicles";
import GarageCustomers from "./pages/garage/Customers";
import GarageReports from "./pages/garage/Reports";
import GarageNotifications from "./pages/garage/Notifications";
import GarageSettings from "./pages/garage/Settings";

// Admin pages
import AdminDashboard from "./pages/admin/AdminDashboard";
import UsersManagement from "./pages/admin/UsersManagement";
import GaragesManagement from "./pages/admin/GaragesManagement";
import OrdersOverview from "./pages/admin/OrdersOverview";
import FinancialReports from "./pages/admin/FinancialReports";
import ReviewsRatings from "./pages/admin/ReviewsRatings";
import Notifications from "./pages/admin/Notifications";
import SupportTickets from "./pages/admin/SupportTickets";
import AdminSettings from "./pages/admin/Settings";

// Other pages
import NotFound from "./pages/NotFound";

const App = () => (
  <QueryProvider>
    <ThemeProvider defaultTheme="light" storageKey="car-service-theme">
      <AuthProvider>
        <NotificationProvider>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <Routes>
                {/* Public auth routes */}
                <Route path="/auth/login" element={
                  <PublicRoute>
                    <Login />
                  </PublicRoute>
                } />
                <Route path="/auth/signup" element={
                  <PublicRoute>
                    <Signup />
                  </PublicRoute>
                } />
                <Route path="/auth/forgot-password" element={
                  <PublicRoute>
                    <ForgotPassword />
                  </PublicRoute>
                } />
                <Route path="/auth/verify-email" element={
                  <PublicRoute>
                    <VerifyEmail />
                  </PublicRoute>
                } />
                <Route path="/auth/reset-password" element={
                  <PublicRoute>
                    <ResetPassword />
                  </PublicRoute>
                } />
                <Route path="/auth/callback" element={<AuthCallback />} />

                {/* Admin routes */}
                <Route path="/admin/*" element={
                  <AdminRoute>
                    <AdminLayout />
                  </AdminRoute>
                }>
                  <Route index element={<AdminDashboard />} />
                  <Route path="users" element={<UsersManagement />} />
                  <Route path="garages" element={<GaragesManagement />} />
                  <Route path="orders" element={<OrdersOverview />} />
                  <Route path="reports" element={<FinancialReports />} />
                  <Route path="reviews" element={<ReviewsRatings />} />
                  <Route path="notifications" element={<Notifications />} />
                  <Route path="support" element={<SupportTickets />} />
                  <Route path="settings" element={<AdminSettings />} />
                </Route>

                {/* Garage owner routes */}
                <Route path="/garage/*" element={
                  <GarageOwnerRoute>
                    <SidebarProvider>
                      <div className="flex min-h-screen w-full">
                        <AppSidebar />
                        <SidebarInset className="flex-1">
                          <Header />
                          <Routes>
                            <Route index element={<GarageDashboard />} />
                            <Route path="orders" element={<GarageOrders />} />
                            <Route path="services" element={<GarageServices />} />
                            <Route path="vehicles" element={<GarageVehicles />} />
                            <Route path="customers" element={<GarageCustomers />} />
                            <Route path="reports" element={<GarageReports />} />
                            <Route path="notifications" element={<GarageNotifications />} />
                            <Route path="settings" element={<GarageSettings />} />
                          </Routes>
                        </SidebarInset>
                      </div>
                    </SidebarProvider>
                  </GarageOwnerRoute>
                } />

                {/* Customer routes */}
                <Route path="/customer/*" element={
                  <CustomerRoute>
                    <SidebarProvider>
                      <div className="flex min-h-screen w-full">
                        <AppSidebar />
                        <SidebarInset className="flex-1">
                          <Header />
                          <Routes>
                            <Route index element={<Dashboard />} />
                            <Route path="vehicles" element={<Vehicles />} />
                            <Route path="vehicle/:id" element={<VehicleDetails />} />
                            <Route path="upload" element={<Upload />} />
                            <Route path="invoices" element={<Invoices />} />
                            <Route path="reports" element={<Reports />} />
                            <Route path="notifications" element={<CustomerNotifications />} />
                            <Route path="settings" element={<Settings />} />
                            <Route path="garages" element={<Garages />} />
                            <Route path="garage/:id" element={<GarageDetails />} />
                          </Routes>
                        </SidebarInset>
                      </div>
                    </SidebarProvider>
                  </CustomerRoute>
                } />

                {/* Default redirect */}
                <Route path="/" element={
                  <ProtectedRoute>
                    <div>Redirecting...</div>
                  </ProtectedRoute>
                } />

                {/* 404 */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </BrowserRouter>
          </TooltipProvider>
        </NotificationProvider>
      </AuthProvider>
    </ThemeProvider>
  </QueryProvider>
);

export default App;
