import { useState } from "react";
import { Settings, Car, Calendar, Bell, Shield, Wrench } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";

interface VehicleSettingsModalProps {
  children: React.ReactNode;
  vehicleName?: string;
}

export function VehicleSettingsModal({ children, vehicleName = "المركبة" }: VehicleSettingsModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [settings, setSettings] = useState({
    notifications: true,
    autoReminders: true,
    locationTracking: false,
    fuelTracking: true,
    maintenanceAlerts: true,
    documentReminders: true,
  });
  const { toast } = useToast();

  const handleSettingChange = (key: string, value: boolean) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleSave = () => {
    toast({
      title: "تم حفظ الإعدادات",
      description: `تم تحديث إعدادات ${vehicleName} بنجاح`,
    });
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-almarai flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary-dark rounded-lg flex items-center justify-center">
              <Settings className="w-4 h-4 text-primary-foreground" />
            </div>
            إعدادات {vehicleName}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Notifications Settings */}
          <Card className="premium-card p-6">
            <div className="flex items-center gap-2 mb-4">
              <Bell className="w-5 h-5 text-primary" />
              <h3 className="font-arabic font-semibold">إعدادات الإشعارات</h3>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="font-arabic">الإشعارات العامة</Label>
                  <p className="text-sm text-muted-foreground">تلقي جميع الإشعارات المتعلقة بالمركبة</p>
                </div>
                <Switch
                  checked={settings.notifications}
                  onCheckedChange={(value) => handleSettingChange('notifications', value)}
                />
              </div>
              
              <Separator />
              
              <div className="flex items-center justify-between">
                <div>
                  <Label className="font-arabic">تذكيرات تلقائية</Label>
                  <p className="text-sm text-muted-foreground">تذكيرات للصيانة والمواعيد المهمة</p>
                </div>
                <Switch
                  checked={settings.autoReminders}
                  onCheckedChange={(value) => handleSettingChange('autoReminders', value)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label className="font-arabic">تنبيهات الصيانة</Label>
                  <p className="text-sm text-muted-foreground">تنبيهات عند اقتراب موعد الصيانة</p>
                </div>
                <Switch
                  checked={settings.maintenanceAlerts}
                  onCheckedChange={(value) => handleSettingChange('maintenanceAlerts', value)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <Label className="font-arabic">تذكيرات الوثائق</Label>
                  <p className="text-sm text-muted-foreground">تذكيرات انتهاء الرخصة والتأمين</p>
                </div>
                <Switch
                  checked={settings.documentReminders}
                  onCheckedChange={(value) => handleSettingChange('documentReminders', value)}
                />
              </div>
            </div>
          </Card>

          {/* Tracking Settings */}
          <Card className="premium-card p-6">
            <div className="flex items-center gap-2 mb-4">
              <Shield className="w-5 h-5 text-primary" />
              <h3 className="font-arabic font-semibold">إعدادات التتبع</h3>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="font-arabic">تتبع الموقع</Label>
                  <p className="text-sm text-muted-foreground">تتبع موقع المركبة (يتطلب جهاز GPS)</p>
                </div>
                <Switch
                  checked={settings.locationTracking}
                  onCheckedChange={(value) => handleSettingChange('locationTracking', value)}
                />
              </div>
              
              <Separator />
              
              <div className="flex items-center justify-between">
                <div>
                  <Label className="font-arabic">تتبع استهلاك الوقود</Label>
                  <p className="text-sm text-muted-foreground">تسجيل وتتبع استهلاك الوقود</p>
                </div>
                <Switch
                  checked={settings.fuelTracking}
                  onCheckedChange={(value) => handleSettingChange('fuelTracking', value)}
                />
              </div>
            </div>
          </Card>

          {/* Quick Actions */}
          <Card className="premium-card p-6">
            <div className="flex items-center gap-2 mb-4">
              <Wrench className="w-5 h-5 text-primary" />
              <h3 className="font-arabic font-semibold">إجراءات سريعة</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <Button variant="outline" className="h-12 font-arabic justify-start">
                <Calendar className="w-4 h-4 ml-2" />
                جدولة صيانة
              </Button>
              <Button variant="outline" className="h-12 font-arabic justify-start">
                <Car className="w-4 h-4 ml-2" />
                إضافة ملاحظة
              </Button>
              <Button variant="outline" className="h-12 font-arabic justify-start">
                <Shield className="w-4 h-4 ml-2" />
                تحديث التأمين
              </Button>
              <Button variant="outline" className="h-12 font-arabic justify-start">
                <Bell className="w-4 h-4 ml-2" />
                اختبار الإشعارات
              </Button>
            </div>
          </Card>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button onClick={handleSave} className="flex-1 btn-premium text-white font-arabic gap-2">
              <Settings className="w-4 h-4" />
              حفظ الإعدادات
            </Button>
            <Button type="button" variant="outline" onClick={() => setIsOpen(false)} className="font-arabic">
              إلغاء
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}