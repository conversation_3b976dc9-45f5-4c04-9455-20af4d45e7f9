import { supabase } from '@/integrations/supabase/client'
import type { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types'

export type Garage = Tables<'garages'>
export type GarageInsert = TablesInsert<'garages'>
export type GarageUpdate = TablesUpdate<'garages'>

export class GaragesAPI {
  // Get all active garages
  static async getActiveGarages(): Promise<Garage[]> {
    const { data, error } = await supabase
      .from('garages')
      .select('*')
      .eq('is_active', true)
      .order('name')

    if (error) throw error
    return data || []
  }

  // Get garage by ID
  static async getGarageById(id: string): Promise<Garage | null> {
    const { data, error } = await supabase
      .from('garages')
      .select('*')
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  }

  // Get garages by owner
  static async getGaragesByOwner(ownerId: string): Promise<Garage[]> {
    const { data, error } = await supabase
      .from('garages')
      .select('*')
      .eq('owner_id', ownerId)
      .order('name')

    if (error) throw error
    return data || []
  }

  // Create garage
  static async createGarage(garage: GarageInsert): Promise<Garage> {
    const { data, error } = await supabase
      .from('garages')
      .insert(garage)
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Update garage
  static async updateGarage(id: string, updates: GarageUpdate): Promise<Garage> {
    const { data, error } = await supabase
      .from('garages')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Delete garage
  static async deleteGarage(id: string): Promise<void> {
    const { error } = await supabase
      .from('garages')
      .delete()
      .eq('id', id)

    if (error) throw error
  }

  // Get garage with services
  static async getGarageWithServices(id: string) {
    const { data, error } = await supabase
      .from('garages')
      .select(`
        *,
        services (*)
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  }

  // Search garages
  static async searchGarages(query: string): Promise<Garage[]> {
    const { data, error } = await supabase
      .from('garages')
      .select('*')
      .eq('is_active', true)
      .or(`name.ilike.%${query}%,description.ilike.%${query}%,address.ilike.%${query}%`)
      .order('name')

    if (error) throw error
    return data || []
  }
}
