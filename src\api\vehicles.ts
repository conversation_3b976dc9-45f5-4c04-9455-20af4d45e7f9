import { supabase } from '@/integrations/supabase/client'
import type { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types'

export type Vehicle = Tables<'vehicles'>
export type VehicleInsert = TablesInsert<'vehicles'>
export type VehicleUpdate = TablesUpdate<'vehicles'>

export class VehiclesAPI {
  // Get vehicles by customer
  static async getVehiclesByCustomer(customerId: string): Promise<Vehicle[]> {
    const { data, error } = await supabase
      .from('vehicles')
      .select('*')
      .eq('customer_id', customerId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  }

  // Get vehicle by ID
  static async getVehicleById(id: string): Promise<Vehicle | null> {
    const { data, error } = await supabase
      .from('vehicles')
      .select('*')
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  }

  // Create vehicle
  static async createVehicle(vehicle: VehicleInsert): Promise<Vehicle> {
    const { data, error } = await supabase
      .from('vehicles')
      .insert(vehicle)
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Update vehicle
  static async updateVehicle(id: string, updates: VehicleUpdate): Promise<Vehicle> {
    const { data, error } = await supabase
      .from('vehicles')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Delete vehicle
  static async deleteVehicle(id: string): Promise<void> {
    const { error } = await supabase
      .from('vehicles')
      .delete()
      .eq('id', id)

    if (error) throw error
  }

  // Get vehicle with orders
  static async getVehicleWithOrders(id: string) {
    const { data, error } = await supabase
      .from('vehicles')
      .select(`
        *,
        orders (
          *,
          garage:garages (
            name,
            address
          ),
          order_services (
            *,
            service:services (
              name,
              price
            )
          )
        )
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  }

  // Search vehicles by license plate
  static async searchVehiclesByLicense(query: string): Promise<Vehicle[]> {
    const { data, error } = await supabase
      .from('vehicles')
      .select('*')
      .ilike('license_plate', `%${query}%`)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  }

  // Get vehicles for garage (for orders)
  static async getVehiclesForGarage(garageId: string): Promise<Vehicle[]> {
    const { data, error } = await supabase
      .from('vehicles')
      .select(`
        *,
        orders!inner (
          garage_id
        )
      `)
      .eq('orders.garage_id', garageId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  }
}
