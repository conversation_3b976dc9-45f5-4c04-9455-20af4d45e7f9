import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Search,
  Filter,
  Download,
  Eye,
  Ban,
  CheckCircle,
  Trash2,
  UserPlus,
  Users,
  UserCheck,
  UserX,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  status: "active" | "blocked" | "pending";
  registrationDate: string;
  lastActive: string;
  totalOrders: number;
  totalSpent: number;
  city: string;
}

const mockUsers: User[] = [
  {
    id: "1",
    name: "أحمد محمد الأحمد",
    email: "<EMAIL>",
    phone: "+966501234567",
    status: "active",
    registrationDate: "2024-01-15",
    lastActive: "2024-01-20",
    totalOrders: 15,
    totalSpent: 3500,
    city: "الرياض",
  },
  {
    id: "2",
    name: "فاطمة علي السالم",
    email: "<EMAIL>",
    phone: "+966509876543",
    status: "active",
    registrationDate: "2024-01-10",
    lastActive: "2024-01-19",
    totalOrders: 8,
    totalSpent: 2100,
    city: "جدة",
  },
  {
    id: "3",
    name: "محمد سعد الغامدي",
    email: "<EMAIL>",
    phone: "+966505555555",
    status: "blocked",
    registrationDate: "2024-01-05",
    lastActive: "2024-01-18",
    totalOrders: 3,
    totalSpent: 850,
    city: "الدمام",
  },
  {
    id: "4",
    name: "سارة أحمد الخليفة",
    email: "<EMAIL>",
    phone: "+966507777777",
    status: "pending",
    registrationDate: "2024-01-18",
    lastActive: "2024-01-18",
    totalOrders: 0,
    totalSpent: 0,
    city: "مكة",
  },
  {
    id: "5",
    name: "خالد عبدالله النعيمي",
    email: "<EMAIL>",
    phone: "+966503333333",
    status: "active",
    registrationDate: "2023-12-20",
    lastActive: "2024-01-17",
    totalOrders: 22,
    totalSpent: 5200,
    city: "المدينة",
  },
];

const statusConfig = {
  active: { label: "نشط", color: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300", icon: CheckCircle },
  blocked: { label: "محظور", color: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300", icon: Ban },
  pending: { label: "في الانتظار", color: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300", icon: UserPlus },
};

export default function UsersManagement() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [cityFilter, setCityFilter] = useState<string>("all");
  const [users, setUsers] = useState(mockUsers);
  const { toast } = useToast();

  const filteredUsers = users.filter(user => {
    const matchesSearch = 
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.phone.includes(searchTerm);
    
    const matchesStatus = statusFilter === "all" || user.status === statusFilter;
    const matchesCity = cityFilter === "all" || user.city === cityFilter;
    
    return matchesSearch && matchesStatus && matchesCity;
  });

  const getStatusBadge = (status: User['status']) => {
    const config = statusConfig[status];
    const IconComponent = config.icon;
    return (
      <Badge className={`${config.color} flex items-center gap-1 px-3 py-1`}>
        <IconComponent className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  const handleAction = (action: string, user: User) => {
    toast({
      title: "تم تنفيذ الإجراء",
      description: `تم ${action} للمستخدم ${user.name}`,
    });
    
    if (action === "حظر" || action === "إلغاء الحظر") {
      setUsers(users.map(u => 
        u.id === user.id 
          ? { ...u, status: u.status === "blocked" ? "active" : "blocked" as const }
          : u
      ));
    }
  };

  const getUserStats = () => {
    return {
      total: users.length,
      active: users.filter(u => u.status === "active").length,
      blocked: users.filter(u => u.status === "blocked").length,
      pending: users.filter(u => u.status === "pending").length,
    };
  };

  const stats = getUserStats();

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground">إدارة المستخدمين</h1>
          <p className="text-muted-foreground">إدارة حسابات العملاء والمستخدمين</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 ml-2" />
            تصدير
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100/50 dark:from-blue-900/20 dark:to-blue-800/10 border-blue-200 dark:border-blue-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-700 dark:text-blue-300">إجمالي المستخدمين</p>
                <p className="text-2xl font-bold text-blue-800 dark:text-blue-200">{stats.total}</p>
              </div>
              <Users className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-green-100/50 dark:from-green-900/20 dark:to-green-800/10 border-green-200 dark:border-green-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-green-700 dark:text-green-300">المستخدمون النشطون</p>
                <p className="text-2xl font-bold text-green-800 dark:text-green-200">{stats.active}</p>
              </div>
              <UserCheck className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-red-50 to-red-100/50 dark:from-red-900/20 dark:to-red-800/10 border-red-200 dark:border-red-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-red-700 dark:text-red-300">المحظورون</p>
                <p className="text-2xl font-bold text-red-800 dark:text-red-200">{stats.blocked}</p>
              </div>
              <UserX className="w-8 h-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100/50 dark:from-yellow-900/20 dark:to-yellow-800/10 border-yellow-200 dark:border-yellow-800/30">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-yellow-700 dark:text-yellow-300">في الانتظار</p>
                <p className="text-2xl font-bold text-yellow-800 dark:text-yellow-200">{stats.pending}</p>
              </div>
              <UserPlus className="w-8 h-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="البحث بالاسم، البريد الإلكتروني، رقم الهاتف..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="فلترة بالحالة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع الحالات</SelectItem>
                <SelectItem value="active">نشط</SelectItem>
                <SelectItem value="blocked">محظور</SelectItem>
                <SelectItem value="pending">في الانتظار</SelectItem>
              </SelectContent>
            </Select>
            <Select value={cityFilter} onValueChange={setCityFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="فلترة بالمدينة" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">جميع المدن</SelectItem>
                <SelectItem value="الرياض">الرياض</SelectItem>
                <SelectItem value="جدة">جدة</SelectItem>
                <SelectItem value="الدمام">الدمام</SelectItem>
                <SelectItem value="مكة">مكة</SelectItem>
                <SelectItem value="المدينة">المدينة</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            قائمة المستخدمين ({filteredUsers.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow className="bg-muted/50">
                  <TableHead className="text-right">الاسم</TableHead>
                  <TableHead className="text-right">البريد والهاتف</TableHead>
                  <TableHead className="text-right">المدينة</TableHead>
                  <TableHead className="text-right">تاريخ التسجيل</TableHead>
                  <TableHead className="text-right">آخر نشاط</TableHead>
                  <TableHead className="text-right">الطلبات</TableHead>
                  <TableHead className="text-right">إجمالي الإنفاق</TableHead>
                  <TableHead className="text-right">الحالة</TableHead>
                  <TableHead className="text-right">الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.id} className="hover:bg-muted/30 transition-colors">
                    <TableCell className="font-medium">{user.name}</TableCell>
                    <TableCell>
                      <div>
                        <p className="text-sm">{user.email}</p>
                        <p className="text-sm text-muted-foreground">{user.phone}</p>
                      </div>
                    </TableCell>
                    <TableCell>{user.city}</TableCell>
                    <TableCell className="text-sm">{user.registrationDate}</TableCell>
                    <TableCell className="text-sm">{user.lastActive}</TableCell>
                    <TableCell>
                      <Badge variant="outline">{user.totalOrders} طلب</Badge>
                    </TableCell>
                    <TableCell className="font-medium">{user.totalSpent.toLocaleString()} ريال</TableCell>
                    <TableCell>{getStatusBadge(user.status)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button size="sm" variant="outline" className="h-8 w-8 p-0" onClick={() => handleAction('عرض التفاصيل', user)}>
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline" 
                          className="h-8 w-8 p-0" 
                          onClick={() => handleAction(user.status === "blocked" ? "إلغاء الحظر" : "حظر", user)}
                        >
                          {user.status === "blocked" ? <CheckCircle className="w-4 h-4" /> : <Ban className="w-4 h-4" />}
                        </Button>
                        <Button size="sm" variant="outline" className="h-8 w-8 p-0 text-destructive hover:text-destructive" onClick={() => handleAction('حذف', user)}>
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}