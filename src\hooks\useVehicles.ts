import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { VehiclesAPI, type VehicleInsert, type VehicleUpdate } from '@/api/vehicles'
import { queryKeys } from '@/lib/react-query'
import { toast } from 'sonner'

export function useVehiclesByCustomer(customerId: string) {
  return useQuery({
    queryKey: queryKeys.vehicles.byCustomer(customerId),
    queryFn: () => VehiclesAPI.getVehiclesByCustomer(customerId),
    enabled: !!customerId,
  })
}

export function useVehicle(id: string) {
  return useQuery({
    queryKey: queryKeys.vehicles.byId(id),
    queryFn: () => VehiclesAPI.getVehicleById(id),
    enabled: !!id,
  })
}

export function useVehicleWithOrders(id: string) {
  return useQuery({
    queryKey: [...queryKeys.vehicles.byId(id), 'with-orders'],
    queryFn: () => VehiclesAPI.getVehicleWithOrders(id),
    enabled: !!id,
  })
}

export function useVehiclesForGarage(garageId: string) {
  return useQuery({
    queryKey: ['vehicles', 'garage', garageId],
    queryFn: () => VehiclesAPI.getVehiclesForGarage(garageId),
    enabled: !!garageId,
  })
}

export function useCreateVehicle() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (vehicle: VehicleInsert) => VehiclesAPI.createVehicle(vehicle),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.vehicles.all() })
      if (data.customer_id) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.vehicles.byCustomer(data.customer_id) 
        })
      }
      toast.success('تم إضافة المركبة بنجاح')
    },
    onError: () => {
      toast.error('حدث خطأ في إضافة المركبة')
    },
  })
}

export function useUpdateVehicle() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: VehicleUpdate }) =>
      VehiclesAPI.updateVehicle(id, updates),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.vehicles.all() })
      queryClient.invalidateQueries({ queryKey: queryKeys.vehicles.byId(data.id) })
      if (data.customer_id) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.vehicles.byCustomer(data.customer_id) 
        })
      }
      toast.success('تم تحديث المركبة بنجاح')
    },
    onError: () => {
      toast.error('حدث خطأ في تحديث المركبة')
    },
  })
}

export function useDeleteVehicle() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => VehiclesAPI.deleteVehicle(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.vehicles.all() })
      toast.success('تم حذف المركبة بنجاح')
    },
    onError: () => {
      toast.error('حدث خطأ في حذف المركبة')
    },
  })
}

export function useSearchVehicles(query: string) {
  return useQuery({
    queryKey: ['vehicles', 'search', query],
    queryFn: () => VehiclesAPI.searchVehiclesByLicense(query),
    enabled: query.length > 0,
  })
}
