import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"

interface StatusBadgeProps {
  status: string
  className?: string
}

const statusConfig = {
  // Order statuses
  pending: {
    label: "معلق",
    variant: "secondary" as const,
    className: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
  },
  in_progress: {
    label: "قيد التنفيذ",
    variant: "default" as const,
    className: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
  },
  completed: {
    label: "مكتمل",
    variant: "default" as const,
    className: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
  },
  cancelled: {
    label: "ملغي",
    variant: "destructive" as const,
    className: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
  },
  
  // Vehicle statuses
  active: {
    label: "نشط",
    variant: "default" as const,
    className: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
  },
  maintenance: {
    label: "صيانة",
    variant: "secondary" as const,
    className: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300"
  },
  inactive: {
    label: "غير نشط",
    variant: "outline" as const,
    className: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
  },
  
  // User roles
  admin: {
    label: "مدير",
    variant: "default" as const,
    className: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300"
  },
  garage_owner: {
    label: "مالك ورشة",
    variant: "secondary" as const,
    className: "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300"
  },
  customer: {
    label: "عميل",
    variant: "outline" as const,
    className: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
  },
  
  // General statuses
  verified: {
    label: "موثق",
    variant: "default" as const,
    className: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
  },
  unverified: {
    label: "غير موثق",
    variant: "secondary" as const,
    className: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
  },
  blocked: {
    label: "محظور",
    variant: "destructive" as const,
    className: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
  }
}

export function StatusBadge({ status, className }: StatusBadgeProps) {
  const config = statusConfig[status as keyof typeof statusConfig] || {
    label: status,
    variant: "outline" as const,
    className: ""
  }

  return (
    <Badge 
      variant={config.variant}
      className={cn(config.className, className)}
    >
      {config.label}
    </Badge>
  )
}
