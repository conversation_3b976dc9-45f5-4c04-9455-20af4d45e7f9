import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { OrdersAPI, type OrderInsert, type OrderUpdate } from '@/api/orders'
import { queryKeys } from '@/lib/react-query'
import { toast } from 'sonner'

export function useOrdersByCustomer(customerId: string) {
  return useQuery({
    queryKey: queryKeys.orders.byCustomer(customerId),
    queryFn: () => OrdersAPI.getOrdersByCustomer(customerId),
    enabled: !!customerId,
  })
}

export function useOrdersByGarage(garageId: string) {
  return useQuery({
    queryKey: queryKeys.orders.byGarage(garageId),
    queryFn: () => OrdersAPI.getOrdersByGarage(garageId),
    enabled: !!garageId,
  })
}

export function useOrder(id: string) {
  return useQuery({
    queryKey: queryKeys.orders.byId(id),
    queryFn: () => OrdersAPI.getOrderById(id),
    enabled: !!id,
  })
}

export function useOrdersByStatus(status: string) {
  return useQuery({
    queryKey: queryKeys.orders.byStatus(status),
    queryFn: () => OrdersAPI.getOrdersByStatus(status),
    enabled: !!status,
  })
}

export function useRecentOrders(limit: number = 10) {
  return useQuery({
    queryKey: ['orders', 'recent', limit],
    queryFn: () => OrdersAPI.getRecentOrders(limit),
  })
}

export function useOrderStats() {
  return useQuery({
    queryKey: ['orders', 'stats'],
    queryFn: () => OrdersAPI.getOrderStats(),
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

export function useCreateOrder() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ 
      order, 
      services 
    }: { 
      order: OrderInsert; 
      services: Array<{ service_id: string; quantity: number; price: number }> 
    }) => OrdersAPI.createOrder(order, services),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.all() })
      if (data.customer_id) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.orders.byCustomer(data.customer_id) 
        })
      }
      if (data.garage_id) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.orders.byGarage(data.garage_id) 
        })
      }
      queryClient.invalidateQueries({ queryKey: ['orders', 'stats'] })
      toast.success('تم إنشاء الطلب بنجاح')
    },
    onError: () => {
      toast.error('حدث خطأ في إنشاء الطلب')
    },
  })
}

export function useUpdateOrder() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: OrderUpdate }) =>
      OrdersAPI.updateOrder(id, updates),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.all() })
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.byId(data.id) })
      if (data.customer_id) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.orders.byCustomer(data.customer_id) 
        })
      }
      if (data.garage_id) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.orders.byGarage(data.garage_id) 
        })
      }
      queryClient.invalidateQueries({ queryKey: ['orders', 'stats'] })
      toast.success('تم تحديث الطلب بنجاح')
    },
    onError: () => {
      toast.error('حدث خطأ في تحديث الطلب')
    },
  })
}

export function useUpdateOrderStatus() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: string }) =>
      OrdersAPI.updateOrderStatus(id, status),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.all() })
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.byId(data.id) })
      if (data.customer_id) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.orders.byCustomer(data.customer_id) 
        })
      }
      if (data.garage_id) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.orders.byGarage(data.garage_id) 
        })
      }
      queryClient.invalidateQueries({ queryKey: ['orders', 'stats'] })
      
      // Show appropriate success message based on status
      const statusMessages = {
        pending: 'تم تعيين الطلب كمعلق',
        in_progress: 'تم بدء العمل على الطلب',
        completed: 'تم إكمال الطلب بنجاح',
        cancelled: 'تم إلغاء الطلب',
      }
      
      toast.success(statusMessages[status as keyof typeof statusMessages] || 'تم تحديث حالة الطلب')
    },
    onError: () => {
      toast.error('حدث خطأ في تحديث حالة الطلب')
    },
  })
}

export function useDeleteOrder() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => OrdersAPI.deleteOrder(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.all() })
      queryClient.invalidateQueries({ queryKey: ['orders', 'stats'] })
      toast.success('تم حذف الطلب بنجاح')
    },
    onError: () => {
      toast.error('حدث خطأ في حذف الطلب')
    },
  })
}
