import { supabase } from '@/integrations/supabase/client'
import type { AuthUser, UserRole } from '@/integrations/supabase/types'

export interface SignUpData {
  email: string
  password: string
  fullName: string
  phone?: string
}

export interface SignInData {
  email: string
  password: string
}

export interface ResetPasswordData {
  email: string
}

export interface UpdatePasswordData {
  password: string
}

export interface AuthResponse {
  user: AuthUser | null
  error: string | null
}

export class AuthAPI {
  // Sign up (customers only)
  static async signUp(data: SignUpData): Promise<AuthResponse> {
    try {
      const { data: authData, error } = await supabase.auth.signUp({
        email: data.email,
        password: data.password,
        options: {
          data: {
            full_name: data.fullName,
            phone: data.phone,
            role: 'customer', // Only customers can sign up
          },
        },
      })

      if (error) {
        return { user: null, error: error.message }
      }

      if (!authData.user) {
        return { user: null, error: 'فشل في إنشاء الحساب' }
      }

      return {
        user: {
          id: authData.user.id,
          email: authData.user.email!,
          role: 'customer',
          full_name: data.fullName,
          phone: data.phone,
        },
        error: null,
      }
    } catch (error) {
      return { user: null, error: 'حدث خطأ غير متوقع' }
    }
  }

  // Sign in
  static async signIn(data: SignInData): Promise<AuthResponse> {
    try {
      const { data: authData, error } = await supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password,
      })

      if (error) {
        return { user: null, error: 'البريد الإلكتروني أو كلمة المرور غير صحيحة' }
      }

      if (!authData.user) {
        return { user: null, error: 'فشل في تسجيل الدخول' }
      }

      // Get user profile to get role
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', authData.user.id)
        .single()

      if (profileError || !profile) {
        return { user: null, error: 'فشل في جلب بيانات المستخدم' }
      }

      return {
        user: {
          id: authData.user.id,
          email: authData.user.email!,
          role: profile.role as UserRole,
          full_name: profile.full_name,
          phone: profile.phone,
          avatar_url: profile.avatar_url,
        },
        error: null,
      }
    } catch (error) {
      return { user: null, error: 'حدث خطأ غير متوقع' }
    }
  }

  // Sign in with Google (customers only)
  static async signInWithGoogle(): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      })

      if (error) {
        return { error: error.message }
      }

      return { error: null }
    } catch (error) {
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Sign out
  static async signOut(): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase.auth.signOut()

      if (error) {
        return { error: error.message }
      }

      return { error: null }
    } catch (error) {
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Reset password
  static async resetPassword(data: ResetPasswordData): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(data.email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      })

      if (error) {
        return { error: error.message }
      }

      return { error: null }
    } catch (error) {
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Update password
  static async updatePassword(data: UpdatePasswordData): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase.auth.updateUser({
        password: data.password,
      })

      if (error) {
        return { error: error.message }
      }

      return { error: null }
    } catch (error) {
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Get current user
  static async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser()

      if (!user) {
        return null
      }

      // Get user profile to get role
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (error || !profile) {
        return null
      }

      return {
        id: user.id,
        email: user.email!,
        role: profile.role as UserRole,
        full_name: profile.full_name,
        phone: profile.phone,
        avatar_url: profile.avatar_url,
      }
    } catch (error) {
      return null
    }
  }

  // Get current session
  static async getSession() {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      return session
    } catch (error) {
      return null
    }
  }

  // Listen to auth changes
  static onAuthStateChange(callback: (user: AuthUser | null) => void) {
    return supabase.auth.onAuthStateChange(async (event, session) => {
      if (session?.user) {
        const user = await this.getCurrentUser()
        callback(user)
      } else {
        callback(null)
      }
    })
  }

  // Verify email with OTP
  static async verifyOtp(email: string, token: string): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase.auth.verifyOtp({
        email,
        token,
        type: 'email',
      })

      if (error) {
        return { error: error.message }
      }

      return { error: null }
    } catch (error) {
      return { error: 'حدث خطأ غير متوقع' }
    }
  }

  // Resend verification email
  static async resendVerification(email: string): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email,
      })

      if (error) {
        return { error: error.message }
      }

      return { error: null }
    } catch (error) {
      return { error: 'حدث خطأ غير متوقع' }
    }
  }
}
