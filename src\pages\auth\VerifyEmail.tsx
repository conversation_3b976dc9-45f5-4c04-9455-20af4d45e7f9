import { useState, useEffect } from 'react'
import { useLocation, Link } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Mail, ArrowRight, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuthContext } from '@/contexts/AuthContext'

const verifyEmailSchema = z.object({
  token: z.string().min(6, 'رمز التحقق يجب أن يكون 6 أرقام على الأقل'),
})

type VerifyEmailFormData = z.infer<typeof verifyEmailSchema>

export function VerifyEmail() {
  const location = useLocation()
  const email = location.state?.email || ''
  const [countdown, setCountdown] = useState(60)
  const [canResend, setCanResend] = useState(false)
  
  const { 
    verifyOtp, 
    resendVerification, 
    isVerifyingOtp, 
    isResendingVerification 
  } = useAuthContext()

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<VerifyEmailFormData>({
    resolver: zodResolver(verifyEmailSchema),
  })

  // Countdown timer for resend button
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000)
      return () => clearTimeout(timer)
    } else {
      setCanResend(true)
    }
  }, [countdown])

  const onSubmit = (data: VerifyEmailFormData) => {
    if (email) {
      verifyOtp({ email, token: data.token })
    }
  }

  const handleResendVerification = () => {
    if (email && canResend) {
      resendVerification(email)
      setCountdown(60)
      setCanResend(false)
    }
  }

  if (!email) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-bold">خطأ</CardTitle>
            <CardDescription>
              لم يتم العثور على بريد إلكتروني للتحقق
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <Link
                to="/auth/signup"
                className="inline-flex items-center text-sm text-primary hover:underline"
              >
                <ArrowRight className="ml-1 h-4 w-4" />
                العودة إلى إنشاء الحساب
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
            <Mail className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <CardTitle className="text-2xl font-bold">تحقق من بريدك الإلكتروني</CardTitle>
          <CardDescription>
            لقد أرسلنا رمز التحقق إلى
            <br />
            <span className="font-medium text-foreground">{email}</span>
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="token">رمز التحقق</Label>
              <Input
                id="token"
                type="text"
                placeholder="123456"
                className="text-center text-lg tracking-widest"
                maxLength={6}
                {...register('token')}
              />
              {errors.token && (
                <p className="text-sm text-destructive">{errors.token.message}</p>
              )}
            </div>

            <Button
              type="submit"
              className="w-full"
              disabled={isVerifyingOtp}
            >
              {isVerifyingOtp ? 'جاري التحقق...' : 'تحقق من الرمز'}
            </Button>
          </form>

          <div className="text-center space-y-2">
            <p className="text-sm text-muted-foreground">
              لم تستلم الرمز؟
            </p>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handleResendVerification}
              disabled={!canResend || isResendingVerification}
              className="text-primary hover:text-primary/80"
            >
              {isResendingVerification ? (
                <>
                  <RefreshCw className="ml-1 h-4 w-4 animate-spin" />
                  جاري الإرسال...
                </>
              ) : canResend ? (
                'إعادة إرسال الرمز'
              ) : (
                `إعادة الإرسال خلال ${countdown} ثانية`
              )}
            </Button>
          </div>

          <div className="text-center">
            <Link
              to="/auth/login"
              className="inline-flex items-center text-sm text-primary hover:underline"
            >
              <ArrowRight className="ml-1 h-4 w-4" />
              العودة إلى تسجيل الدخول
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
