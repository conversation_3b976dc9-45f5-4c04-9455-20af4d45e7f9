import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { 
  Plus, 
  Search, 
  Filter, 
  Send, 
  Edit, 
  Trash2, 
  Bell, 
  Users, 
  MessageSquare, 
  UserCheck, 
  Car, 
  CreditCard, 
  Star, 
  Archive, 
  Forward, 
  Settings,
  TrendingUp,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Off
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface GarageNotification {
  id: string;
  title: string;
  description: string;
  sentDate: string;
  recipients: string[];
  readCount: number;
  totalRecipients: number;
  type: 'new_request' | 'vehicle_update' | 'payment_update' | 'feedback' | 'general';
  status: 'sent' | 'draft' | 'scheduled' | 'new' | 'read';
  priority: 'low' | 'medium' | 'high';
  actionRequired?: boolean;
  orderId?: string;
}

const garageNotificationTypes = [
  { id: "new_request", label: "طلبات خدمة جديدة", icon: UserCheck, color: "bg-gradient-to-br from-emerald-600 to-emerald-700" },
  { id: "vehicle_update", label: "تحديثات المركبات", icon: Car, color: "bg-gradient-to-br from-blue-600 to-blue-700" },
  { id: "payment_update", label: "تحديثات الدفع", icon: CreditCard, color: "bg-gradient-to-br from-green-600 to-green-700" },
  { id: "feedback", label: "التقييمات والملاحظات", icon: Star, color: "bg-gradient-to-br from-amber-600 to-orange-600" },
  { id: "general", label: "إشعارات إدارية", icon: AlertTriangle, color: "bg-gradient-to-br from-slate-600 to-slate-700" }
];

const mockGarageNotifications: GarageNotification[] = [
  {
    id: '1',
    title: 'طلب خدمة جديد من أحمد محمد',
    description: 'طلب صيانة دورية لسيارة تويوتا كامري 2022 - رقم اللوحة: أ ب ج 123',
    sentDate: '2025-01-09 14:30',
    recipients: [],
    readCount: 0,
    totalRecipients: 0,
    type: 'new_request',
    status: 'new',
    priority: 'high',
    actionRequired: true,
    orderId: 'ORD-001'
  },
  {
    id: '2',
    title: 'مركبة جاهزة للاستلام',
    description: 'تم الانتهاء من صيانة سيارة فاطمة حسن وهي جاهزة للاستلام',
    sentDate: '2025-01-09 12:15',
    recipients: [],
    readCount: 0,
    totalRecipients: 0,
    type: 'vehicle_update',
    status: 'new',
    priority: 'medium',
    actionRequired: true,
    orderId: 'ORD-002'
  },
  {
    id: '3',
    title: 'تأكيد دفعة جديدة',
    description: 'تم استلام دفعة بقيمة 1,500 ريال من العميل محمد سالم',
    sentDate: '2025-01-09 10:45',
    recipients: [],
    readCount: 1,
    totalRecipients: 1,
    type: 'payment_update',
    status: 'read',
    priority: 'low',
    actionRequired: false
  },
  {
    id: '4',
    title: 'تقييم جديد: 5 نجوم',
    description: 'حصلت على تقييم ممتاز من العميل سارة أحمد مع تعليق إيجابي',
    sentDate: '2025-01-08 16:20',
    recipients: [],
    readCount: 1,
    totalRecipients: 1,
    type: 'feedback',
    status: 'read',
    priority: 'low',
    actionRequired: false
  },
  {
    id: '5',
    title: 'موعد صيانة دورية غداً',
    description: 'لديك 3 مواعيد صيانة دورية مجدولة ليوم غد',
    sentDate: '2025-01-08 09:00',
    recipients: [],
    readCount: 1,
    totalRecipients: 1,
    type: 'general',
    status: 'read',
    priority: 'medium',
    actionRequired: false
  }
];

interface GarageNotificationCardProps {
  notification: GarageNotification;
  onMarkAsRead: (id: string) => void;
  onArchive: (id: string) => void;
  onAcceptRequest?: (id: string) => void;
  onReplyFeedback?: (id: string) => void;
}

function GarageNotificationCard({ notification, onMarkAsRead, onArchive, onAcceptRequest, onReplyFeedback }: GarageNotificationCardProps) {
  const typeInfo = garageNotificationTypes.find(t => t.id === notification.type);
  const Icon = typeInfo?.icon || Bell;
  
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return "الآن";
    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;
    if (diffInHours < 48) return "أمس";
    return date.toLocaleDateString('ar-SA', { 
      day: 'numeric', 
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPriorityStyle = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-red-500/30 bg-red-50/50 dark:border-red-400/30 dark:bg-red-950/30';
      case 'medium': return 'border-amber-500/30 bg-amber-50/50 dark:border-amber-400/30 dark:bg-amber-950/30';
      default: return 'border-border bg-card/80';
    }
  };

  return (
    <Card className={`group transition-all duration-300 hover:shadow-xl hover:-translate-y-1 ${
      notification.status === 'new' ? 'ring-2 ring-primary/40 shadow-lg' : ''
    } ${getPriorityStyle(notification.priority)} backdrop-blur-sm`}>
      <CardContent className="p-6">
        <div className="flex items-start gap-4">
          <div className={`w-16 h-16 rounded-2xl ${typeInfo?.color || 'bg-gradient-to-br from-primary to-primary-dark'} 
            flex items-center justify-center text-white shadow-xl group-hover:scale-105 transition-transform duration-200 flex-shrink-0`}>
            <Icon className="w-8 h-8" />
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-3 mb-3">
              <h3 className="font-almarai font-bold text-xl text-foreground leading-tight">
                {notification.title}
              </h3>
              <div className="flex items-center gap-2 flex-shrink-0">
                {notification.status === 'new' && (
                  <Badge className="bg-gradient-to-r from-primary to-primary-dark text-primary-foreground border-0 shadow-md animate-pulse">
                    جديد
                  </Badge>
                )}
                {notification.priority === 'high' && (
                  <Badge variant="destructive" className="bg-gradient-to-r from-red-600 to-red-700 border-0">
                    عاجل
                  </Badge>
                )}
                {notification.actionRequired && (
                  <Badge className="bg-gradient-to-r from-amber-500 to-amber-600 text-white border-0">
                    يتطلب إجراء
                  </Badge>
                )}
              </div>
            </div>
            
            <p className="text-muted-foreground leading-relaxed mb-4 font-arabic">
              {notification.description}
            </p>
            
            {notification.orderId && (
              <div className="mb-4">
                <span className="text-xs font-mono bg-muted/80 px-2 py-1 rounded-md">
                  رقم الطلب: {notification.orderId}
                </span>
              </div>
            )}
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground font-arabic bg-muted/60 px-3 py-1.5 rounded-full">
                {formatTime(notification.sentDate)}
              </span>
              
              <div className="flex items-center gap-2">
                {notification.type === 'new_request' && notification.actionRequired && onAcceptRequest && (
                  <Button
                    size="sm"
                    onClick={() => onAcceptRequest(notification.id)}
                    className="bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white border-0"
                  >
                    <CheckCircle className="w-4 h-4 ml-1" />
                    قبول الطلب
                  </Button>
                )}
                
                {notification.type === 'feedback' && onReplyFeedback && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onReplyFeedback(notification.id)}
                    className="border-blue-200 text-blue-700 hover:bg-blue-50 dark:border-blue-800 dark:text-blue-300"
                  >
                    <MessageSquare className="w-4 h-4 ml-1" />
                    رد
                  </Button>
                )}
                
                {notification.status === 'new' && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onMarkAsRead(notification.id)}
                    className="text-primary hover:bg-primary/10"
                  >
                    <CheckCircle className="w-4 h-4 ml-1" />
                    تحديد كمقروء
                  </Button>
                )}
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onArchive(notification.id)}
                  className="text-muted-foreground hover:text-foreground hover:bg-muted/50"
                >
                  <Archive className="w-4 h-4 ml-1" />
                  أرشفة
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

const GarageNotifications = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [notifications, setNotifications] = useState(mockGarageNotifications);
  const [isNewNotificationModalOpen, setIsNewNotificationModalOpen] = useState(false);
  const [newNotificationTitle, setNewNotificationTitle] = useState("");
  const [newNotificationMessage, setNewNotificationMessage] = useState("");
  const [recipientType, setRecipientType] = useState("all");
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([]);
  const [notificationSettings, setNotificationSettings] = useState<Record<string, boolean>>({});
  const { toast } = useToast();

  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = notification.title.includes(searchTerm) || 
                         notification.description.includes(searchTerm);
    const matchesFilter = filterType === "all" || notification.type === filterType;
    return matchesSearch && matchesFilter;
  });

  const unreadCount = notifications.filter(n => n.status === 'new').length;
  const actionRequiredCount = notifications.filter(n => n.actionRequired).length;

  const handleSendNewNotification = () => {
    if (!newNotificationTitle || !newNotificationMessage) {
      toast({
        title: "خطأ",
        description: "يرجى ملء جميع الحقول المطلوبة",
        variant: "destructive",
      });
      return;
    }

    const newNotification: GarageNotification = {
      id: Date.now().toString(),
      title: newNotificationTitle,
      description: newNotificationMessage,
      sentDate: new Date().toLocaleString('ar-SA'),
      recipients: recipientType === "all" ? ["جميع العملاء"] : selectedCustomers,
      readCount: 0,
      totalRecipients: recipientType === "all" ? 25 : selectedCustomers.length,
      type: 'general',
      status: 'sent',
      priority: 'medium',
      actionRequired: false
    };

    setNotifications([newNotification, ...notifications]);
    toast({
      title: "تم إرسال الإشعار",
      description: "تم إرسال الإشعار بنجاح لجميع المستلمين",
    });

    setNewNotificationTitle("");
    setNewNotificationMessage("");
    setRecipientType("all");
    setSelectedCustomers([]);
    setIsNewNotificationModalOpen(false);
  };

  const handleMarkAsRead = (notificationId: string) => {
    setNotifications(notifications.map(n => 
      n.id === notificationId ? { ...n, status: 'read' as const } : n
    ));
    toast({
      title: "تم تحديد الإشعار كمقروء",
      description: "تم تحديث حالة الإشعار بنجاح",
    });
  };

  const handleArchiveNotification = (notificationId: string) => {
    setNotifications(notifications.filter(n => n.id !== notificationId));
    toast({
      title: "تم أرشفة الإشعار",
      description: "تم نقل الإشعار إلى الأرشيف",
    });
  };

  const handleAcceptRequest = (notificationId: string) => {
    setNotifications(notifications.map(n => 
      n.id === notificationId ? { ...n, status: 'read' as const, actionRequired: false } : n
    ));
    toast({
      title: "تم قبول الطلب",
      description: "تم قبول طلب الخدمة وإرسال إشعار للعميل",
    });
  };

  const handleReplyFeedback = (notificationId: string) => {
    toast({
      title: "فتح نافذة الرد",
      description: "يمكنك الآن الرد على تقييم العميل",
    });
  };

  const handleMarkAllAsRead = () => {
    setNotifications(notifications.map(n => ({ ...n, status: 'read' as const })));
    toast({
      title: "تم تحديد جميع الإشعارات كمقروءة",
      description: "تم تحديث حالة جميع الإشعارات بنجاح"
    });
  };

  const handleSettingToggle = (type: string) => {
    setNotificationSettings(prev => ({
      ...prev,
      [type]: !prev[type]
    }));
    toast({
      title: "تم تحديث الإعدادات",
      description: `تم ${notificationSettings[type] ? 'إيقاف' : 'تفعيل'} إشعارات ${garageNotificationTypes.find(t => t.id === type)?.label}`
    });
  };

  const getTypeLabel = (type: GarageNotification['type']) => {
    const typeLabels = {
      new_request: 'طلب جديد',
      vehicle_update: 'تحديث مركبة',
      payment_update: 'تحديث دفع',
      feedback: 'تقييم',
      general: 'عام'
    };
    return typeLabels[type];
  };

  const getTypeBadgeColor = (type: GarageNotification['type']) => {
    const colors = {
      new_request: 'bg-emerald-500/10 text-emerald-700 border-emerald-500/20',
      vehicle_update: 'bg-blue-500/10 text-blue-700 border-blue-500/20',
      payment_update: 'bg-green-500/10 text-green-700 border-green-500/20',
      feedback: 'bg-amber-500/10 text-amber-700 border-amber-500/20',
      general: 'bg-slate-500/10 text-slate-700 border-slate-500/20'
    };
    return colors[type];
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-slate-50/80 to-slate-100 dark:from-slate-950 dark:via-slate-950/95 dark:to-slate-900" dir="rtl">
      <div className="container mx-auto p-4 md:p-6 space-y-6 max-w-7xl">
        {/* Professional Header */}
        <div className="space-y-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 bg-gradient-to-br from-slate-700 via-slate-800 to-slate-900 rounded-2xl flex items-center justify-center shadow-2xl">
                <Bell className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl md:text-4xl font-almarai font-bold bg-gradient-to-r from-slate-900 to-slate-700 dark:from-slate-100 dark:to-slate-300 bg-clip-text text-transparent">
                  إدارة الإشعارات
                </h1>
                <p className="text-muted-foreground font-arabic text-lg">
                  {unreadCount > 0 ? `${unreadCount} إشعار جديد` : 'لا توجد إشعارات جديدة'}
                  {actionRequiredCount > 0 && ` • ${actionRequiredCount} يتطلب إجراء`}
                </p>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                variant="outline"
                onClick={handleMarkAllAsRead}
                disabled={unreadCount === 0}
                className="flex items-center gap-2 bg-card/80 border-border/50 hover:bg-primary/10 hover:border-primary/30"
              >
                <CheckCircle className="w-4 h-4" />
                تحديد الكل كمقروء
              </Button>
              <Button 
                onClick={() => setIsNewNotificationModalOpen(true)}
                className="bg-gradient-to-r from-slate-700 to-slate-800 hover:from-slate-800 hover:to-slate-900 text-white font-arabic shadow-lg"
              >
                <Plus className="h-4 w-4 ml-2" />
                إرسال إشعار جديد
              </Button>
            </div>
          </div>

          {/* Enhanced Search and Filters */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
              <Input
                placeholder="البحث في الإشعارات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-12 font-arabic h-12 bg-card/80 border-border/50 focus:border-primary/50 focus:ring-primary/20"
              />
            </div>
            
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-full md:w-64 h-12 bg-card/80 border-border/50">
                <Filter className="h-4 w-4 ml-2" />
                <SelectValue placeholder="تصفية حسب النوع" />
              </SelectTrigger>
              <SelectContent align="start" side="bottom" className="bg-background border border-border shadow-lg z-50">
                <SelectItem value="all">جميع الإشعارات</SelectItem>
                {garageNotificationTypes.map((type) => (
                  <SelectItem key={type.id} value={type.id}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Professional Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card className="bg-gradient-to-br from-card/90 to-card/60 backdrop-blur-sm border-border/50 shadow-lg">
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-slate-600 to-slate-700 rounded-xl flex items-center justify-center mx-auto mb-3">
                  <Bell className="h-6 w-6 text-white" />
                </div>
                <p className="text-3xl font-bold text-foreground">{notifications.length}</p>
                <p className="text-sm text-muted-foreground font-arabic">إجمالي الإشعارات</p>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-br from-card/90 to-card/60 backdrop-blur-sm border-border/50 shadow-lg">
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                  <AlertTriangle className="h-6 w-6 text-white" />
                </div>
                <p className="text-3xl font-bold text-red-600">{unreadCount}</p>
                <p className="text-sm text-muted-foreground font-arabic">إشعارات جديدة</p>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-br from-card/90 to-card/60 backdrop-blur-sm border-border/50 shadow-lg">
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                  <Clock className="h-6 w-6 text-white" />
                </div>
                <p className="text-3xl font-bold text-amber-600">{actionRequiredCount}</p>
                <p className="text-sm text-muted-foreground font-arabic">يتطلب إجراء</p>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-br from-card/90 to-card/60 backdrop-blur-sm border-border/50 shadow-lg">
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                  <TrendingUp className="h-6 w-6 text-white" />
                </div>
                <p className="text-3xl font-bold text-emerald-600">
                  {notifications.filter(n => n.type === 'new_request').length}
                </p>
                <p className="text-sm text-muted-foreground font-arabic">طلبات جديدة</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Professional Tabs Interface */}
        <Tabs defaultValue="notifications" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 bg-card/80 border border-border/50 backdrop-blur-sm h-12">
            <TabsTrigger 
              value="notifications" 
              className="font-arabic data-[state=active]:bg-gradient-to-r data-[state=active]:from-slate-700 data-[state=active]:to-slate-800 data-[state=active]:text-white"
            >
              الإشعارات ({filteredNotifications.length})
            </TabsTrigger>
            <TabsTrigger 
              value="settings" 
              className="font-arabic data-[state=active]:bg-gradient-to-r data-[state=active]:from-slate-700 data-[state=active]:to-slate-800 data-[state=active]:text-white"
            >
              الإعدادات
            </TabsTrigger>
          </TabsList>

          <TabsContent value="notifications" className="space-y-4">
            {filteredNotifications.length === 0 ? (
              <Card className="bg-gradient-to-br from-card/80 to-card/40 backdrop-blur-sm border-border/50">
                <CardContent className="flex flex-col items-center justify-center py-16">
                  <div className="w-20 h-20 bg-gradient-to-br from-muted to-muted/50 rounded-full flex items-center justify-center mb-6">
                    <BellOff className="w-10 h-10 text-muted-foreground" />
                  </div>
                  <h3 className="text-xl font-almarai font-bold text-foreground mb-3">
                    لا توجد إشعارات
                  </h3>
                  <p className="text-muted-foreground text-center font-arabic max-w-md">
                    {searchTerm || filterType !== "all" 
                      ? "لم يتم العثور على إشعارات تطابق البحث المحدد"
                      : "لا توجد إشعارات في الوقت الحالي. ستظهر هنا عندما تتوفر"
                    }
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {filteredNotifications.map((notification) => (
                  <GarageNotificationCard
                    key={notification.id}
                    notification={notification}
                    onMarkAsRead={handleMarkAsRead}
                    onArchive={handleArchiveNotification}
                    onAcceptRequest={handleAcceptRequest}
                    onReplyFeedback={handleReplyFeedback}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="settings">
            <Card className="bg-gradient-to-br from-card/90 to-card/60 backdrop-blur-sm border-border/50 shadow-lg">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-slate-700 to-slate-800 rounded-xl flex items-center justify-center shadow-lg">
                    <Settings className="w-5 h-5 text-white" />
                  </div>
                  <h2 className="text-xl font-almarai font-bold">إعدادات الإشعارات</h2>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {garageNotificationTypes.map((type) => {
                  const Icon = type.icon;
                  return (
                    <div key={type.id} className="flex items-center justify-between p-4 rounded-xl bg-muted/20 hover:bg-muted/40 transition-colors border border-border/30">
                      <div className="flex items-center gap-4">
                        <div className={`w-12 h-12 ${type.color} rounded-xl flex items-center justify-center text-white shadow-lg`}>
                          <Icon className="w-6 h-6" />
                        </div>
                        <div>
                          <span className="font-arabic font-medium text-foreground text-lg">{type.label}</span>
                          <p className="text-sm text-muted-foreground font-arabic">
                            {type.id === "new_request" ? "إشعارات الطلبات الجديدة وقبولها" :
                             type.id === "vehicle_update" ? "تحديثات حالة المركبات" :
                             type.id === "payment_update" ? "إشعارات الدفعات والفواتير" :
                             type.id === "feedback" ? "تقييمات وملاحظات العملاء" :
                             "الإشعارات الإدارية العامة"}
                          </p>
                        </div>
                      </div>
                      <Switch
                        checked={notificationSettings[type.id] ?? true}
                        onCheckedChange={() => handleSettingToggle(type.id)}
                        className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                      />
                    </div>
                  );
                })}
                
                <div className="mt-8 p-6 bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-900/50 dark:to-slate-800/50 rounded-xl border border-slate-200 dark:border-slate-700">
                  <div className="flex items-center gap-3 mb-4">
                    <Bell className="w-5 h-5 text-slate-700 dark:text-slate-300" />
                    <span className="font-arabic font-medium text-slate-900 dark:text-slate-100 text-lg">إعدادات الإشعارات المباشرة</span>
                  </div>
                  <p className="text-sm text-slate-700 dark:text-slate-300 font-arabic mb-4">
                    إدارة إعدادات الإشعارات المباشرة للكراج والموظفين
                  </p>
                  <div className="flex gap-3">
                    <Button variant="outline" size="sm" className="text-slate-700 border-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800">
                      إعدادات الفريق
                    </Button>
                    <Button variant="outline" size="sm" className="text-slate-700 border-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800">
                      إدارة الإشعارات المباشرة
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* New Notification Modal */}
      <Dialog open={isNewNotificationModalOpen} onOpenChange={setIsNewNotificationModalOpen}>
        <DialogContent className="max-w-2xl" dir="rtl">
          <DialogHeader>
            <DialogTitle className="font-arabic">إرسال إشعار جديد</DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="title" className="font-arabic">عنوان الإشعار *</Label>
              <Input
                id="title"
                value={newNotificationTitle}
                onChange={(e) => setNewNotificationTitle(e.target.value)}
                placeholder="اكتب عنوان الإشعار..."
                className="font-arabic"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="message" className="font-arabic">محتوى الإشعار *</Label>
              <Textarea
                id="message"
                value={newNotificationMessage}
                onChange={(e) => setNewNotificationMessage(e.target.value)}
                placeholder="اكتب محتوى الإشعار..."
                className="font-arabic"
                rows={4}
              />
            </div>

            <div className="space-y-4">
              <Label className="font-arabic">المستلمين</Label>
              <Select value={recipientType} onValueChange={setRecipientType}>
                <SelectTrigger className="font-arabic">
                  <SelectValue placeholder="اختر المستلمين" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all" className="font-arabic">جميع العملاء</SelectItem>
                  <SelectItem value="specific" className="font-arabic">عملاء محددين</SelectItem>
                  <SelectItem value="active_orders" className="font-arabic">العملاء ذوي الطلبات النشطة</SelectItem>
                  <SelectItem value="ready_orders" className="font-arabic">العملاء ذوي الطلبات الجاهزة</SelectItem>
                </SelectContent>
              </Select>

              {recipientType === "specific" && (
                <div className="space-y-2">
                  <Label className="font-arabic">اختر العملاء</Label>
                  <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                    {['أحمد محمد', 'فاطمة حسن', 'محمد سالم', 'سارة أحمد'].map((customer) => (
                      <div key={customer} className="flex items-center space-x-2">
                        <Checkbox
                          id={customer}
                          checked={selectedCustomers.includes(customer)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedCustomers([...selectedCustomers, customer]);
                            } else {
                              setSelectedCustomers(selectedCustomers.filter(c => c !== customer));
                            }
                          }}
                        />
                        <Label htmlFor={customer} className="font-arabic text-sm">{customer}</Label>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="flex gap-2 pt-4">
              <Button onClick={handleSendNewNotification} className="font-arabic">
                <Send className="h-4 w-4 ml-2" />
                إرسال الإشعار
              </Button>
              <Button variant="outline" onClick={() => setIsNewNotificationModalOpen(false)} className="font-arabic">
                إلغاء
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default GarageNotifications;