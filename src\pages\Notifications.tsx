import { useState } from "react";
import { <PERSON>, <PERSON>, <PERSON>ff, <PERSON>rash2, <PERSON><PERSON><PERSON><PERSON>, Set<PERSON>s, Car, CreditCard, FileText, Wrench, AlertTriangle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "@/hooks/use-toast";
import { useNotifications } from "@/contexts/NotificationContext";

const notificationTypes = [
  { id: "maintenance", label: "صيانة", icon: Wrench, color: "bg-blue-500" },
  { id: "payment", label: "دفعات", icon: Credit<PERSON><PERSON>, color: "bg-green-500" },
  { id: "license", label: "ترخيص", icon: FileText, color: "bg-orange-500" },
  { id: "repair", label: "إصلاح", icon: Car, color: "bg-purple-500" },
  { id: "general", label: "تنبيهات عامة", icon: AlertTriangle, color: "bg-gray-500" }
];

interface NotificationCardProps {
  notification: any;
  onMarkAsRead: (id: number) => void;
  onDelete: (id: number) => void;
}

function NotificationCard({ notification, onMarkAsRead, onDelete }: NotificationCardProps) {
  const typeInfo = notificationTypes.find(t => t.id === notification.type);
  const Icon = typeInfo?.icon || Bell;
  
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return "الآن";
    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;
    if (diffInHours < 48) return "أمس";
    return date.toLocaleDateString('ar-SA', { 
      day: 'numeric', 
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-destructive/10 border-destructive/20';
      case 'medium': return 'bg-warning/10 border-warning/20';
      default: return 'bg-muted/50 border-border/50';
    }
  };

  return (
    <Card className={`premium-card transition-all duration-300 hover:shadow-lg ${
      notification.status === 'new' ? 'ring-2 ring-primary/20' : ''
    } ${getPriorityColor(notification.priority)}`}>
      <CardContent className="p-4 md:p-6">
        <div className="flex items-start gap-4">
          {/* Icon */}
          <div className={`w-12 h-12 rounded-xl ${typeInfo?.color || 'bg-primary'} flex items-center justify-center text-white flex-shrink-0`}>
            <Icon className="w-6 h-6" />
          </div>
          
          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2 mb-2">
              <h3 className="font-almarai font-bold text-lg text-foreground leading-tight">
                {notification.title}
              </h3>
              <div className="flex items-center gap-2 flex-shrink-0">
                {notification.status === 'new' && (
                  <Badge variant="default" className="bg-primary/10 text-primary border-primary/20">
                    جديد
                  </Badge>
                )}
                {notification.priority === 'high' && (
                  <Badge variant="destructive" className="bg-destructive/10 text-destructive border-destructive/20">
                    مهم
                  </Badge>
                )}
              </div>
            </div>
            
            <p className="text-muted-foreground text-sm md:text-base leading-relaxed mb-3">
              {notification.description}
            </p>
            
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground font-arabic">
                {formatTime(notification.timestamp)}
              </span>
              
              <div className="flex items-center gap-2">
                {notification.status === 'new' && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onMarkAsRead(notification.id)}
                    className="h-8 px-3 text-xs"
                  >
                    <CheckCheck className="w-4 h-4 ml-1" />
                    تحديد كمقروء
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDelete(notification.id)}
                  className="h-8 px-3 text-xs text-destructive hover:text-destructive"
                >
                  <Trash2 className="w-4 h-4 ml-1" />
                  حذف
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

interface NotificationSettingsProps {
  settings: Record<string, boolean>;
  onToggle: (type: string) => void;
}

function NotificationSettings({ settings, onToggle }: NotificationSettingsProps) {
  return (
    <Card className="premium-card">
      <CardHeader>
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary-dark rounded-lg flex items-center justify-center">
            <Settings className="w-4 h-4 text-primary-foreground" />
          </div>
          <h2 className="text-xl font-almarai font-bold">إعدادات الإشعارات</h2>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {notificationTypes.map((type) => {
          const Icon = type.icon;
          return (
            <div key={type.id} className="flex items-center justify-between p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors">
              <div className="flex items-center gap-3">
                <div className={`w-8 h-8 ${type.color} rounded-lg flex items-center justify-center text-white`}>
                  <Icon className="w-4 h-4" />
                </div>
                <span className="font-arabic font-medium">{type.label}</span>
              </div>
              <Switch
                checked={settings[type.id] ?? true}
                onCheckedChange={() => onToggle(type.id)}
              />
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
}

export default function Notifications() {
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    deleteAllNotifications
  } = useNotifications();
  
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedType, setSelectedType] = useState("all");
  const [notificationSettings, setNotificationSettings] = useState<Record<string, boolean>>({});

  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = notification.title.includes(searchQuery) || 
                         notification.description.includes(searchQuery);
    const matchesType = selectedType === "all" || notification.type === selectedType;
    return matchesSearch && matchesType;
  });

  // Use unreadCount from context instead of calculating it

  const handleMarkAllAsRead = () => {
    markAllAsRead();
    toast({
      title: "تم تحديد جميع الإشعارات كمقروءة",
      description: "تم تحديث حالة جميع الإشعارات بنجاح"
    });
  };

  const handleDeleteAll = () => {
    deleteAllNotifications();
    toast({
      title: "تم حذف جميع الإشعارات",
      description: "تم حذف جميع الإشعارات من النظام"
    });
  };

  const handleMarkAsRead = (id: number) => {
    markAsRead(id);
    toast({
      title: "تم تحديد الإشعار كمقروء",
      description: "تم تحديث حالة الإشعار بنجاح"
    });
  };

  const handleDelete = (id: number) => {
    deleteNotification(id);
    toast({
      title: "تم حذف الإشعار",
      description: "تم حذف الإشعار من النظام"
    });
  };

  const handleSettingToggle = (type: string) => {
    setNotificationSettings(prev => ({
      ...prev,
      [type]: !prev[type]
    }));
    toast({
      title: "تم تحديث الإعدادات",
      description: `تم ${notificationSettings[type] ? 'إيقاف' : 'تفعيل'} إشعارات ${notificationTypes.find(t => t.id === type)?.label}`
    });
  };

  return (
    <div className="container mx-auto p-4 md:p-6 space-y-6 max-w-7xl">
      {/* Header */}
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary-dark rounded-xl flex items-center justify-center">
              <Bell className="w-6 h-6 text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-2xl md:text-3xl font-almarai font-bold text-foreground">
                الإشعارات
              </h1>
              <p className="text-muted-foreground font-arabic">
                {unreadCount > 0 ? `${unreadCount} إشعار جديد` : 'لا توجد إشعارات جديدة'}
              </p>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              variant="outline"
              onClick={handleMarkAllAsRead}
              disabled={unreadCount === 0}
              className="flex items-center gap-2"
            >
              <CheckCheck className="w-4 h-4" />
              تحديد الكل كمقروء
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteAll}
              disabled={notifications.length === 0}
              className="flex items-center gap-2"
            >
              <Trash2 className="w-4 h-4" />
              حذف جميع الإشعارات
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="البحث في الإشعارات..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pr-10 font-arabic"
            />
          </div>
          
          <Select value={selectedType} onValueChange={setSelectedType}>
            <SelectTrigger className="w-full md:w-48">
              <SelectValue placeholder="تصفية حسب النوع" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع الإشعارات</SelectItem>
              {notificationTypes.map((type) => (
                <SelectItem key={type.id} value={type.id}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="notifications" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="notifications" className="font-arabic">
            الإشعارات ({filteredNotifications.length})
          </TabsTrigger>
          <TabsTrigger value="settings" className="font-arabic">
            الإعدادات
          </TabsTrigger>
        </TabsList>

        <TabsContent value="notifications" className="space-y-4">
          {filteredNotifications.length === 0 ? (
            <Card className="premium-card">
              <CardContent className="flex flex-col items-center justify-center py-12">
                <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
                  <BellOff className="w-8 h-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-almarai font-bold text-foreground mb-2">
                  لا توجد إشعارات
                </h3>
                <p className="text-muted-foreground text-center font-arabic">
                  {searchQuery || selectedType !== "all" 
                    ? "لم يتم العثور على إشعارات تطابق البحث المحدد"
                    : "لا توجد إشعارات في الوقت الحالي"
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-3">
              {filteredNotifications.map((notification) => (
                <NotificationCard
                  key={notification.id}
                  notification={notification}
                  onMarkAsRead={handleMarkAsRead}
                  onDelete={handleDelete}
                />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="settings">
          <NotificationSettings
            settings={notificationSettings}
            onToggle={handleSettingToggle}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}