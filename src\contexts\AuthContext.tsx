import React, { createContext, useContext } from 'react'
import { useAuth } from '@/hooks/useAuth'
import type { AuthUser } from '@/integrations/supabase/types'

interface AuthContextType {
  user: AuthUser | null
  isLoading: boolean
  isAuthenticated: boolean
  isAdmin: boolean
  isGarageOwner: boolean
  isCustomer: boolean
  signUp: (data: any) => void
  signIn: (data: any) => void
  signInWithGoogle: () => void
  signOut: () => void
  resetPassword: (data: any) => void
  updatePassword: (data: any) => void
  verifyOtp: (data: any) => void
  resendVerification: (email: string) => void
  isSigningUp: boolean
  isSigningIn: boolean
  isSigningOut: boolean
  isResettingPassword: boolean
  isUpdatingPassword: boolean
  isVerifyingOtp: boolean
  isResendingVerification: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const auth = useAuth()

  return (
    <AuthContext.Provider value={auth}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuthContext() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider')
  }
  return context
}
