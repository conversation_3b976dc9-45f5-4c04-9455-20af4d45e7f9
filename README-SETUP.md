# Car Garage Management System - Complete Setup Guide

## 🚀 Overview

A complete Arabic RTL car garage management system built with:
- **Frontend**: Vite + React + TypeScript + Tailwind CSS
- **Backend**: Supabase (Database + Auth + Storage)
- **State Management**: Tanstack React Query
- **UI Components**: Shadcn/ui with Arabic RTL support
- **Authentication**: Role-based (Ad<PERSON>, Garage Owner, Customer)

## 📋 Prerequisites

- Node.js 18+ and npm
- Supabase account
- Git

## 🛠️ Installation

### 1. Clone and Install Dependencies

```bash
git clone <your-repo-url>
cd cargarage
npm install
```

### 2. Environment Setup

Create a `.env` file in the root directory:

```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 3. Supabase Setup

#### Option A: Using Our Scripts (Recommended)

```bash
# Run database migration
npm run db:migrate

# Seed sample data
npm run db:seed
```

#### Option B: Manual Setup

1. Go to your Supabase project dashboard
2. Navigate to SQL Editor
3. Copy and run the content from `supabase/migrations/20241210000001_initial_schema.sql`
4. Copy and run the content from `supabase/seed.sql`

### 4. Create Admin and Garage Owner Accounts

Since only customers can sign up through the app, manually create admin and garage owner accounts:

1. Go to Supabase Dashboard → Authentication → Users
2. Click "Add user" and create:

**Admin Account:**
```
Email: <EMAIL>
Password: admin123456
User Metadata: {"role": "admin", "full_name": "مدير النظام"}
```

**Garage Owner Accounts:**
```
Email: <EMAIL>
Password: owner123456
User Metadata: {"role": "garage_owner", "full_name": "مالك مركز الخليج"}

Email: <EMAIL>
Password: owner123456
User Metadata: {"role": "garage_owner", "full_name": "مالك النجم الذهبي"}
```

### 5. Update Garage Ownership

After creating garage owner accounts, update the garages table:

```sql
-- Get user IDs from auth.users table first, then update:
UPDATE public.garages SET owner_id = 'USER_ID_FROM_AUTH' WHERE email = '<EMAIL>';
UPDATE public.garages SET owner_id = 'USER_ID_FROM_AUTH' WHERE email = '<EMAIL>';
```

### 6. Configure Google OAuth (Optional)

1. Go to Supabase Dashboard → Authentication → Providers
2. Enable Google provider
3. Add your Google OAuth credentials
4. Set redirect URL: `http://localhost:5173/auth/callback`

## 🚀 Running the Application

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## 🔐 Authentication & Roles

### User Roles

1. **Admin** (`/admin`)
   - Full system access
   - User management
   - Garage management
   - Financial reports

2. **Garage Owner** (`/garage`)
   - Manage their garage
   - Handle orders
   - Manage services
   - View reports

3. **Customer** (`/customer`)
   - Register vehicles
   - Book services
   - Track orders
   - View invoices

### Test Accounts

- **Admin**: `<EMAIL>` / `admin123456`
- **Garage Owner**: `<EMAIL>` / `owner123456`
- **Customer**: Sign up through the app at `/auth/signup`

## 📱 Features

### ✅ Implemented Features

- **Authentication System**
  - Email/password login
  - Google OAuth for customers
  - Role-based access control
  - Email verification
  - Password reset

- **Customer Features**
  - Vehicle management
  - Service booking
  - Order tracking
  - Dashboard with statistics

- **Garage Owner Features**
  - Order management
  - Service management
  - Customer management
  - Revenue tracking

- **Admin Features**
  - User management
  - Garage management
  - System overview
  - Financial reports

- **UI/UX**
  - Full Arabic RTL support
  - Dark/light mode
  - Mobile responsive
  - Modern design with animations

### 🔧 Technical Features

- **Database**
  - PostgreSQL with Supabase
  - Row-Level Security (RLS)
  - Real-time subscriptions
  - Automatic timestamps

- **Frontend**
  - TypeScript for type safety
  - Tanstack Query for data management
  - Shadcn/ui components
  - Tailwind CSS for styling

- **Development Tools**
  - Database migration scripts
  - Type generation
  - ESLint configuration
  - Hot reload

## 📁 Project Structure

```
src/
├── api/                 # API layer (Supabase calls)
├── components/          # Reusable UI components
│   ├── auth/           # Authentication components
│   ├── dashboard/      # Dashboard components
│   └── ui/             # Base UI components
├── contexts/           # React contexts
├── hooks/              # Custom React hooks
├── integrations/       # Supabase integration
├── lib/                # Utility libraries
├── pages/              # Route components
├── providers/          # App providers
└── router/             # Routing configuration
```

## 🛠️ Available Scripts

```bash
npm run dev              # Start development server
npm run build            # Build for production
npm run preview          # Preview production build
npm run lint             # Run ESLint
npm run db:migrate       # Run database migration
npm run db:seed          # Seed sample data
npm run db:reset         # Reset database (dangerous!)
npm run types:generate   # Generate TypeScript types
npm run supabase:setup   # Setup Supabase CLI
```

## 🔍 Troubleshooting

### Common Issues

1. **Profile not created after signup**
   - Check if the `on_auth_user_created` trigger is working
   - Manually create profile if needed

2. **RLS blocking queries**
   - Ensure user is properly authenticated
   - Check if user has the correct role

3. **Google OAuth not working**
   - Verify redirect URL is correct
   - Check Google OAuth credentials

### Useful SQL Queries

```sql
-- Check all users and their roles
SELECT u.email, p.role, p.full_name 
FROM auth.users u 
LEFT JOIN public.profiles p ON u.id = p.id;

-- Check garage ownership
SELECT g.name, p.full_name as owner_name, p.email 
FROM public.garages g 
LEFT JOIN public.profiles p ON g.owner_id = p.id;

-- Reset user role
UPDATE public.profiles SET role = 'admin' WHERE email = '<EMAIL>';
```

## 🚀 Deployment

### Vercel Deployment

1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Environment Variables for Production

```env
VITE_SUPABASE_URL=your_production_supabase_url
VITE_SUPABASE_ANON_KEY=your_production_anon_key
```

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review Supabase logs
3. Check browser console for errors
4. Verify environment variables

## 🎯 Next Steps

1. Test all authentication flows
2. Create sample data for testing
3. Configure production environment
4. Set up monitoring and analytics
5. Add more advanced features as needed

---

**Happy coding! 🚗💨**
