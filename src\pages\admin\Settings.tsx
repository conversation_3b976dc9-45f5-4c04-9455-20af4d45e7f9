import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Settings as SettingsIcon,
  DollarSign,
  Shield,
  Bell,
  Globe,
  Users,
  Image,
  FileText,
  Save,
  Upload,
  Trash2,
  Plus,
  Edit,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Mock settings data
const platformSettings = {
  general: {
    platformName: "منصة الكراجات",
    supportEmail: "<EMAIL>",
    supportPhone: "+966500000000",
    currency: "SAR",
    language: "ar",
    timezone: "Asia/Riyadh"
  },
  financial: {
    platformCommission: 5,
    paymentGatewayFee: 2.9,
    subscriptionPrices: {
      basic: 99,
      premium: 199,
      enterprise: 399
    },
    withdrawalMinimum: 100,
    withdrawalFee: 5
  },
  notifications: {
    emailNotifications: true,
    smsNotifications: true,
    pushNotifications: true,
    marketingEmails: false
  },
  policies: {
    termsOfService: "شروط الخدمة الخاصة بالمنصة...",
    privacyPolicy: "سياسة الخصوصية...",
    refundPolicy: "سياسة الاسترداد..."
  }
};

const adminUsers = [
  {
    id: "1",
    name: "محمد أحمد",
    email: "<EMAIL>",
    role: "super_admin",
    permissions: ["all"],
    status: "active",
    lastLogin: "2024-01-15T10:30:00"
  },
  {
    id: "2", 
    name: "سارة محمد",
    email: "<EMAIL>",
    role: "admin",
    permissions: ["users", "garages", "orders"],
    status: "active",
    lastLogin: "2024-01-14T16:20:00"
  },
  {
    id: "3",
    name: "عبدالله علي",
    email: "<EMAIL>", 
    role: "moderator",
    permissions: ["reviews", "support"],
    status: "inactive",
    lastLogin: "2024-01-10T09:15:00"
  }
];

const banners = [
  {
    id: "1",
    title: "عرض خاص لشهر يناير",
    description: "خصم 20% على جميع خدمات الصيانة",
    imageUrl: "/api/placeholder/400/200",
    link: "/offers/january",
    isActive: true,
    startDate: "2024-01-01",
    endDate: "2024-01-31"
  },
  {
    id: "2",
    title: "إعلان كراج جديد",
    description: "انضم لشبكة أفضل الكراجات في المملكة", 
    imageUrl: "/api/placeholder/400/200",
    link: "/register-garage",
    isActive: true,
    startDate: "2024-01-01", 
    endDate: "2024-12-31"
  }
];

export default function Settings() {
  const [settings, setSettings] = useState(platformSettings);
  const [admins, setAdmins] = useState(adminUsers);
  const [bannerList, setBannerList] = useState(banners);
  const [newAdmin, setNewAdmin] = useState({
    name: "",
    email: "",
    role: "admin",
    permissions: []
  });
  const { toast } = useToast();

  const handleSaveSettings = () => {
    toast({
      title: "تم حفظ الإعدادات",
      description: "تم حفظ إعدادات المنصة بنجاح",
    });
  };

  const handleAddAdmin = () => {
    if (!newAdmin.name || !newAdmin.email) {
      toast({
        title: "خطأ",
        description: "يرجى ملء جميع الحقول المطلوبة",
        variant: "destructive"
      });
      return;
    }

    const admin = {
      id: (admins.length + 1).toString(),
      ...newAdmin,
      status: "active",
      lastLogin: new Date().toISOString()
    };

    setAdmins([...admins, admin]);
    setNewAdmin({ name: "", email: "", role: "admin", permissions: [] });
    
    toast({
      title: "تم إضافة المدير",
      description: "تم إضافة المدير الجديد بنجاح",
    });
  };

  const handleDeleteAdmin = (id: string) => {
    setAdmins(admins.filter(admin => admin.id !== id));
    toast({
      title: "تم حذف المدير",
      description: "تم حذف المدير من النظام",
    });
  };

  const toggleBannerStatus = (id: string) => {
    setBannerList(bannerList.map(banner => 
      banner.id === id ? { ...banner, isActive: !banner.isActive } : banner
    ));
  };

  const getRoleBadge = (role: string) => {
    switch (role) {
      case "super_admin":
        return <Badge className="bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300 font-arabic">مدير عام</Badge>;
      case "admin":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 font-arabic">مدير</Badge>;
      case "moderator":
        return <Badge className="bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 font-arabic">مشرف</Badge>;
      default:
        return <Badge variant="outline" className="font-arabic">{role}</Badge>;
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-foreground font-arabic">الإعدادات العامة</h1>
          <p className="text-muted-foreground font-arabic">إدارة إعدادات المنصة والصلاحيات</p>
        </div>
        <Button onClick={handleSaveSettings} className="font-arabic">
          <Save className="w-4 h-4 ml-2" />
          حفظ الإعدادات
        </Button>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="general" className="font-arabic">عام</TabsTrigger>
          <TabsTrigger value="financial" className="font-arabic">مالي</TabsTrigger>
          <TabsTrigger value="admins" className="font-arabic">الإداريين</TabsTrigger>
          <TabsTrigger value="policies" className="font-arabic">السياسات</TabsTrigger>
          <TabsTrigger value="banners" className="font-arabic">الإعلانات</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 font-arabic">
                <Globe className="w-5 h-5" />
                الإعدادات العامة
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="font-arabic">اسم المنصة</Label>
                  <Input
                    value={settings.general.platformName}
                    onChange={(e) => setSettings({
                      ...settings,
                      general: { ...settings.general, platformName: e.target.value }
                    })}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label className="font-arabic">العملة</Label>
                  <Select 
                    value={settings.general.currency}
                    onValueChange={(value) => setSettings({
                      ...settings,
                      general: { ...settings.general, currency: value }
                    })}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="SAR">ريال سعودي (SAR)</SelectItem>
                      <SelectItem value="USD">دولار أمريكي (USD)</SelectItem>
                      <SelectItem value="EUR">يورو (EUR)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="font-arabic">بريد الدعم</Label>
                  <Input
                    type="email"
                    value={settings.general.supportEmail}
                    onChange={(e) => setSettings({
                      ...settings,
                      general: { ...settings.general, supportEmail: e.target.value }
                    })}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label className="font-arabic">هاتف الدعم</Label>
                  <Input
                    value={settings.general.supportPhone}
                    onChange={(e) => setSettings({
                      ...settings,
                      general: { ...settings.general, supportPhone: e.target.value }
                    })}
                    className="mt-1"
                  />
                </div>
              </div>
              
              <div className="border-t pt-4">
                <h3 className="font-semibold mb-4 font-arabic">إعدادات الإشعارات</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="font-arabic">إشعارات البريد الإلكتروني</Label>
                    <Switch 
                      checked={settings.notifications.emailNotifications}
                      onCheckedChange={(checked) => setSettings({
                        ...settings,
                        notifications: { ...settings.notifications, emailNotifications: checked }
                      })}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label className="font-arabic">الرسائل النصية</Label>
                    <Switch 
                      checked={settings.notifications.smsNotifications}
                      onCheckedChange={(checked) => setSettings({
                        ...settings,
                        notifications: { ...settings.notifications, smsNotifications: checked }
                      })}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label className="font-arabic">الإشعارات الفورية</Label>
                    <Switch 
                      checked={settings.notifications.pushNotifications}
                      onCheckedChange={(checked) => setSettings({
                        ...settings,
                        notifications: { ...settings.notifications, pushNotifications: checked }
                      })}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="financial" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 font-arabic">
                <DollarSign className="w-5 h-5" />
                الإعدادات المالية
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="font-arabic">عمولة المنصة (%)</Label>
                  <Input
                    type="number"
                    value={settings.financial.platformCommission}
                    onChange={(e) => setSettings({
                      ...settings,
                      financial: { ...settings.financial, platformCommission: Number(e.target.value) }
                    })}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label className="font-arabic">رسوم بوابة الدفع (%)</Label>
                  <Input
                    type="number"
                    step="0.1"
                    value={settings.financial.paymentGatewayFee}
                    onChange={(e) => setSettings({
                      ...settings,
                      financial: { ...settings.financial, paymentGatewayFee: Number(e.target.value) }
                    })}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label className="font-arabic">الحد الأدنى للسحب (ريال)</Label>
                  <Input
                    type="number"
                    value={settings.financial.withdrawalMinimum}
                    onChange={(e) => setSettings({
                      ...settings,
                      financial: { ...settings.financial, withdrawalMinimum: Number(e.target.value) }
                    })}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label className="font-arabic">رسوم السحب (ريال)</Label>
                  <Input
                    type="number"
                    value={settings.financial.withdrawalFee}
                    onChange={(e) => setSettings({
                      ...settings,
                      financial: { ...settings.financial, withdrawalFee: Number(e.target.value) }
                    })}
                    className="mt-1"
                  />
                </div>
              </div>

              <div className="border-t pt-4">
                <h3 className="font-semibold mb-4 font-arabic">أسعار الاشتراكات (ريال/شهر)</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label className="font-arabic">الباقة الأساسية</Label>
                    <Input
                      type="number"
                      value={settings.financial.subscriptionPrices.basic}
                      onChange={(e) => setSettings({
                        ...settings,
                        financial: { 
                          ...settings.financial, 
                          subscriptionPrices: { 
                            ...settings.financial.subscriptionPrices, 
                            basic: Number(e.target.value) 
                          }
                        }
                      })}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label className="font-arabic">الباقة المميزة</Label>
                    <Input
                      type="number"
                      value={settings.financial.subscriptionPrices.premium}
                      onChange={(e) => setSettings({
                        ...settings,
                        financial: { 
                          ...settings.financial, 
                          subscriptionPrices: { 
                            ...settings.financial.subscriptionPrices, 
                            premium: Number(e.target.value) 
                          }
                        }
                      })}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label className="font-arabic">الباقة المؤسسية</Label>
                    <Input
                      type="number"
                      value={settings.financial.subscriptionPrices.enterprise}
                      onChange={(e) => setSettings({
                        ...settings,
                        financial: { 
                          ...settings.financial, 
                          subscriptionPrices: { 
                            ...settings.financial.subscriptionPrices, 
                            enterprise: Number(e.target.value) 
                          }
                        }
                      })}
                      className="mt-1"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="admins" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 font-arabic">
                <Users className="w-5 h-5" />
                إدارة المديرين
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Add New Admin */}
              <div className="border rounded-lg p-4 bg-muted/30">
                <h3 className="font-semibold mb-4 font-arabic">إضافة مدير جديد</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Input
                    placeholder="الاسم"
                    value={newAdmin.name}
                    onChange={(e) => setNewAdmin({...newAdmin, name: e.target.value})}
                  />
                  <Input
                    type="email"
                    placeholder="البريد الإلكتروني"
                    value={newAdmin.email}
                    onChange={(e) => setNewAdmin({...newAdmin, email: e.target.value})}
                  />
                  <div className="flex gap-2">
                    <Select value={newAdmin.role} onValueChange={(value) => setNewAdmin({...newAdmin, role: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="admin">مدير</SelectItem>
                        <SelectItem value="moderator">مشرف</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button onClick={handleAddAdmin} className="font-arabic">
                      <Plus className="w-4 h-4 ml-1" />
                      إضافة
                    </Button>
                  </div>
                </div>
              </div>

              {/* Admin List */}
              <div className="space-y-4">
                {admins.map((admin) => (
                  <div key={admin.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                        <Users className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <p className="font-medium font-arabic">{admin.name}</p>
                        <p className="text-sm text-muted-foreground">{admin.email}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {getRoleBadge(admin.role)}
                      <Badge variant={admin.status === "active" ? "default" : "secondary"} className="font-arabic">
                        {admin.status === "active" ? "نشط" : "غير نشط"}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteAdmin(admin.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="policies" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 font-arabic">
                <FileText className="w-5 h-5" />
                السياسات والشروط
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label className="font-arabic">شروط الخدمة</Label>
                <Textarea
                  value={settings.policies.termsOfService}
                  onChange={(e) => setSettings({
                    ...settings,
                    policies: { ...settings.policies, termsOfService: e.target.value }
                  })}
                  className="mt-1 min-h-32"
                />
              </div>
              <div>
                <Label className="font-arabic">سياسة الخصوصية</Label>
                <Textarea
                  value={settings.policies.privacyPolicy}
                  onChange={(e) => setSettings({
                    ...settings,
                    policies: { ...settings.policies, privacyPolicy: e.target.value }
                  })}
                  className="mt-1 min-h-32"
                />
              </div>
              <div>
                <Label className="font-arabic">سياسة الاسترداد</Label>
                <Textarea
                  value={settings.policies.refundPolicy}
                  onChange={(e) => setSettings({
                    ...settings,
                    policies: { ...settings.policies, refundPolicy: e.target.value }
                  })}
                  className="mt-1 min-h-32"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="banners" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 font-arabic">
                <Image className="w-5 h-5" />
                إدارة الإعلانات والبانرات
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <Button className="font-arabic">
                <Plus className="w-4 h-4 ml-2" />
                إضافة إعلان جديد
              </Button>

              <div className="space-y-4">
                {bannerList.map((banner) => (
                  <div key={banner.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="font-semibold font-arabic">{banner.title}</h3>
                        <p className="text-sm text-muted-foreground font-arabic">{banner.description}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={banner.isActive}
                          onCheckedChange={() => toggleBannerStatus(banner.id)}
                        />
                        <Badge variant={banner.isActive ? "default" : "secondary"} className="font-arabic">
                          {banner.isActive ? "نشط" : "غير نشط"}
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-muted-foreground font-arabic">
                        من {banner.startDate} إلى {banner.endDate}
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm" className="font-arabic">
                          <Edit className="w-4 h-4 ml-1" />
                          تعديل
                        </Button>
                        <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}