import { Navigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { fetchUserDetails } from "../api/user";

const ProtectedRoute = ({
  children,
  allowedRoles,
}: {
  children: JSX.Element;
  allowedRoles: string[];
}) => {
  const { data, isLoading } = useQuery({
    queryKey: ["user"],
    queryFn: fetchUserDetails,
  });

  if (isLoading) return <div>تحميل...</div>;
  if (!data) return <Navigate to="/login" />;

  if (!allowedRoles.includes(data.role)) {
    return <Navigate to="/unauthorized" />;
  }

  return children;
};

export default ProtectedRoute;
