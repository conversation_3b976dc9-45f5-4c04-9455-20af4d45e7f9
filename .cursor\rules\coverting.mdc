---
alwaysApply: true
---

when Convertting the existing Arabic (RTL) Vite React UI screens into fully functional, production-ready components using Supabase for auth/storage and Prisma for database operations.

General approach:

- Use @supabase/supabase-js for all database operations, authentication, and storage.
- Manage all data queries, mutations, and real-time updates directly with Supabase.
- Use Tanstack React Query for efficient data fetching, caching, and background updates.
- Create separate API files for each feature/page (e.g., api/vehicles.ts, api/orders.ts).
- Create custom hooks per page (e.g., useVehicles, useGarageOrders, useAdminUsers).
- Implement row-level security in Supabase to enforce user permissions and roles.
- After login, redirect users to their specific dashboard based on their role ('admin', 'garage_owner', 'customer').
- Maintain exact UI design, Arabic RTL layout, dark/light modes, and all animations.
- Optimize for best performance (avoid unnecessary re-renders, minimal data fetching, lazy load images and large content).
- Fully mobile responsive and production-ready.

- Fully optimized for production with high performance and real-time support if needed.
- Integrated with Supabase Auth and JWT protection.
- Responsive on mobile and desktop, supporting Arabic RTL perfectly.
- Use Supabase as the **only backend solution** (database, authentication, storage, realtime).
- Use @supabase/supabase-js SDK in your Vite React TypeScript app to connect directly.
- Supabase Auth handles login, signup, password reset, and roles.
- Use Supabase Row-Level Security (RLS) for access control and permissions.
- Store user roles as a column in the "users" table (e.g., role: 'admin', 'garage_owner', 'customer').
- Use Tanstack React Query to fetch and cache data efficiently.
- Use custom hooks (e.g., useVehicles, useGarageOrders, useAuthUser) to handle data logic.
- Frontend guards to redirect each user to their specific dashboard depending on their role.
- Fully dynamic, production-ready, highly optimized for performance and real-time updates.


Overall stack
React (with Vite) → fast, modern.

Supabase → DB + Auth + RLS.

TanStack Query → fetching and caching user/session data.

Tailwind CSS → fully RTL, dark/light mode support, fast design.

Zod or React Hook Form + Zod → form validation (Arabic-friendly).


Suggested project structure( example )
src/
│
├── api/           # Supabase and TanStack Query API logic
│   ├── supabase.ts
│   ├── auth.ts
│   └── user.ts
│
├── components/    # UI components (inputs, buttons, etc.)
│
├── hooks/         # Custom hooks (e.g., useUser)
│
├── pages/         # Route-level components
│   ├── auth/
│   │   ├── Login.tsx
│   │   ├── Signup.tsx
│   │   ├── ResetPassword.tsx
│   │   ├── VerifyEmail.tsx
│   │   └── NewPassword.tsx
│   ├── dashboard/
│   │   ├── AdminDashboard.tsx
│   │   ├── GarageDashboard.tsx
│   │   └── CustomerDashboard.tsx
│   └── ...
│
├── router/
│   └── ProtectedRoutes.tsx
│
├── styles/
│
└── main.tsx
