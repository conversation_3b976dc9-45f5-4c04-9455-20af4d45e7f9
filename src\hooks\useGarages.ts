import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { GaragesAPI, type GarageInsert, type GarageUpdate } from '@/api/garages'
import { queryKeys } from '@/lib/react-query'
import { toast } from 'sonner'

export function useGarages() {
  return useQuery({
    queryKey: queryKeys.garages.active(),
    queryFn: GaragesAPI.getActiveGarages,
  })
}

export function useGarage(id: string) {
  return useQuery({
    queryKey: queryKeys.garages.byId(id),
    queryFn: () => GaragesAPI.getGarageById(id),
    enabled: !!id,
  })
}

export function useGaragesByOwner(ownerId: string) {
  return useQuery({
    queryKey: queryKeys.garages.byOwner(ownerId),
    queryFn: () => GaragesAPI.getGaragesByOwner(ownerId),
    enabled: !!ownerId,
  })
}

export function useGarageWithServices(id: string) {
  return useQuery({
    queryKey: [...queryKeys.garages.byId(id), 'with-services'],
    queryFn: () => GaragesAPI.getGarageWithServices(id),
    enabled: !!id,
  })
}

export function useCreateGarage() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (garage: GarageInsert) => GaragesAPI.createGarage(garage),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.garages.all() })
      if (data.owner_id) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.garages.byOwner(data.owner_id) 
        })
      }
      toast.success('تم إنشاء الورشة بنجاح')
    },
    onError: () => {
      toast.error('حدث خطأ في إنشاء الورشة')
    },
  })
}

export function useUpdateGarage() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: GarageUpdate }) =>
      GaragesAPI.updateGarage(id, updates),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.garages.all() })
      queryClient.invalidateQueries({ queryKey: queryKeys.garages.byId(data.id) })
      if (data.owner_id) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.garages.byOwner(data.owner_id) 
        })
      }
      toast.success('تم تحديث الورشة بنجاح')
    },
    onError: () => {
      toast.error('حدث خطأ في تحديث الورشة')
    },
  })
}

export function useDeleteGarage() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: string) => GaragesAPI.deleteGarage(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.garages.all() })
      toast.success('تم حذف الورشة بنجاح')
    },
    onError: () => {
      toast.error('حدث خطأ في حذف الورشة')
    },
  })
}

export function useSearchGarages(query: string) {
  return useQuery({
    queryKey: ['garages', 'search', query],
    queryFn: () => GaragesAPI.searchGarages(query),
    enabled: query.length > 0,
  })
}
