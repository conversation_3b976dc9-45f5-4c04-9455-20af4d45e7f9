import { useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { 
  ArrowR<PERSON>, 
  Edit, 
  Trash2, 
  Calendar,
  FileText,
  Settings,
  MapPin,
  Phone,
  Clock,
  Car,
  Wrench,
  CreditCard,
  AlertTriangle,
  CheckCircle,
  XCircle
} from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { BookingModal } from "@/components/modals/BookingModal";
import { EditVehicleModal } from "@/components/modals/EditVehicleModal";
import { EmergencyServiceModal } from "@/components/modals/EmergencyServiceModal";
import { DeleteConfirmModal } from "@/components/modals/DeleteConfirmModal";

// Mock data - replace with real data
const vehicleData = {
  id: 1,
  name: "سيارتي الأساسية",
  model: "تويوتا كامري 2022",
  chassisNumber: "JTDKN3DU8E0123456",
  licensePlate: "أ ب ج 1234",
  status: "جاهزة",
  statusType: "ready" as const,
  licenseExpiry: "2025-03-15",
  image: "/api/placeholder/400/300",
  color: "أبيض لؤلؤي",
  mileage: "45,000 كم",
  lastService: "2024-10-15",
  nextService: "2024-12-15",
  owner: "أحمد محمد",
  phoneNumber: "+966 50 123 4567",
  registrationDate: "2022-01-15",
  insurance: {
    company: "الراجحي تكافل",
    expiry: "2025-01-15",
    policyNumber: "POL-*********"
  }
};

const serviceHistory = [
  {
    id: 1,
    date: "2024-10-15",
    garage: "كراج الخليج المتطور",
    service: "صيانة دورية شاملة",
    cost: "850 ريال",
    status: "مكتملة"
  },
  {
    id: 2,
    date: "2024-08-20",
    garage: "مركز صيانة النجم",
    service: "تغيير زيت المحرك",
    cost: "200 ريال",
    status: "مكتملة"
  },
  {
    id: 3,
    date: "2024-06-10",
    garage: "كراج التميز للسيارات",
    service: "إصلاح الفرامل",
    cost: "450 ريال",
    status: "مكتملة"
  }
];

const statusColors = {
  ready: "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400",
  maintenance: "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400",
  pending: "bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400",
};

export default function VehicleDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("overview");

  return (
    <div className="min-h-screen bg-background mobile-app-layout mobile-safe-area">
      {/* Header */}
      <div className="bg-gradient-to-r from-primary/10 via-primary/5 to-transparent p-4 md:p-6 border-b border-border/50">
        <div className="w-full">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/vehicles")}
              className="gap-2"
            >
              <ArrowRight className="w-4 h-4" />
              العودة للمركبات
            </Button>
          </div>
          
          <div className="flex flex-col md:flex-row md:items-start gap-6">
            <div className="w-full md:w-80 h-48 rounded-xl overflow-hidden">
              <img
                src={vehicleData.image}
                alt={vehicleData.name}
                className="w-full h-full object-cover"
              />
            </div>
            
            <div className="flex-1">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h1 className="text-3xl font-almarai font-bold text-foreground mb-2">
                    {vehicleData.name}
                  </h1>
                  <p className="text-xl text-muted-foreground font-arabic">
                    {vehicleData.model}
                  </p>
                </div>
                
                <div className="flex gap-2">
                  <EditVehicleModal vehicle={vehicleData}>
                    <Button size="sm" className="btn-premium text-white font-arabic">
                      <Edit className="w-4 h-4 ml-2" />
                      تعديل
                    </Button>
                  </EditVehicleModal>
                  <DeleteConfirmModal
                    title="حذف المركبة"
                    description={`هل أنت متأكد من حذف ${vehicleData.name}؟ سيتم حذف جميع البيانات والصور المرتبطة بها.`}
                  >
                    <Button variant="outline" size="sm">
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </DeleteConfirmModal>
                </div>
              </div>
              
              <Badge className={statusColors[vehicleData.statusType]}>
                {vehicleData.status}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      <div className="w-full p-6 space-y-6">
        {/* Tabs */}
        <Card className="premium-card p-6">
          <div className="flex gap-4 border-b border-border/50 pb-4">
            {[
              { id: "overview", label: "نظرة عامة", icon: Car },
              { id: "history", label: "تاريخ الصيانة", icon: Wrench },
              { id: "documents", label: "الوثائق", icon: FileText },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  flex items-center gap-2 px-4 py-2 rounded-lg font-arabic transition-all
                  ${activeTab === tab.id 
                    ? "bg-primary text-primary-foreground" 
                    : "hover:bg-muted/50"
                  }
                `}
              >
                <tab.icon className="w-4 h-4" />
                {tab.label}
              </button>
            ))}
          </div>
        </Card>

        {/* Content */}
        {activeTab === "overview" && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Vehicle Details */}
            <Card className="premium-card p-6">
              <h3 className="font-almarai font-bold text-xl mb-6">تفاصيل المركبة</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground font-arabic">رقم اللوحة:</span>
                  <span className="font-medium">{vehicleData.licensePlate}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground font-arabic">رقم الشاصي:</span>
                  <span className="font-medium text-sm">{vehicleData.chassisNumber}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground font-arabic">اللون:</span>
                  <span className="font-medium">{vehicleData.color}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground font-arabic">المسافة المقطوعة:</span>
                  <span className="font-medium">{vehicleData.mileage}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground font-arabic">تاريخ التسجيل:</span>
                  <span className="font-medium">
                    {new Date(vehicleData.registrationDate).toLocaleDateString('ar-SA')}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground font-arabic">انتهاء الترخيص:</span>
                  <span className={`font-medium ${
                    new Date(vehicleData.licenseExpiry) < new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
                      ? "text-red-600"
                      : "text-green-600"
                  }`}>
                    {new Date(vehicleData.licenseExpiry).toLocaleDateString('ar-SA')}
                  </span>
                </div>
              </div>
            </Card>

            {/* Owner Information */}
            <Card className="premium-card p-6">
              <h3 className="font-almarai font-bold text-xl mb-6">معلومات المالك</h3>
              
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <Avatar className="w-16 h-16">
                    <AvatarImage src="/api/placeholder/64/64" />
                    <AvatarFallback className="bg-gradient-to-br from-primary to-primary-dark text-primary-foreground font-arabic">
                      أح
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-arabic font-medium text-lg">{vehicleData.owner}</p>
                    <p className="text-muted-foreground font-arabic">مالك المركبة</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Phone className="w-5 h-5 text-muted-foreground" />
                  <span className="font-arabic">{vehicleData.phoneNumber}</span>
                </div>
              </div>
            </Card>

            {/* Insurance Information */}
            <Card className="premium-card p-6">
              <h3 className="font-almarai font-bold text-xl mb-6">معلومات التأمين</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground font-arabic">شركة التأمين:</span>
                  <span className="font-medium">{vehicleData.insurance.company}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground font-arabic">رقم البوليصة:</span>
                  <span className="font-medium">{vehicleData.insurance.policyNumber}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground font-arabic">تاريخ الانتهاء:</span>
                  <span className="font-medium">
                    {new Date(vehicleData.insurance.expiry).toLocaleDateString('ar-SA')}
                  </span>
                </div>
              </div>
            </Card>

            {/* Service Information */}
            <Card className="premium-card p-6">
              <h3 className="font-almarai font-bold text-xl mb-6">معلومات الصيانة</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground font-arabic">آخر صيانة:</span>
                  <span className="font-medium">
                    {new Date(vehicleData.lastService).toLocaleDateString('ar-SA')}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground font-arabic">الصيانة القادمة:</span>
                  <span className="font-medium">
                    {new Date(vehicleData.nextService).toLocaleDateString('ar-SA')}
                  </span>
                </div>
                
                <Separator />
                
                <div className="flex gap-3">
                  <BookingModal garageName="كراج معتمد">
                    <Button className="flex-1 btn-premium text-white font-arabic" size="sm">
                      <Calendar className="w-4 h-4 ml-2" />
                      حجز موعد صيانة
                    </Button>
                  </BookingModal>
                  <EmergencyServiceModal>
                    <Button 
                      variant="outline" 
                      className="flex-1 font-arabic" 
                      size="sm"
                    >
                      <Wrench className="w-4 h-4 ml-2" />
                      طلب صيانة طارئة
                    </Button>
                  </EmergencyServiceModal>
                </div>
              </div>
            </Card>
          </div>
        )}

        {activeTab === "history" && (
          <Card className="premium-card p-6">
            <h3 className="font-almarai font-bold text-xl mb-6">تاريخ الصيانة</h3>
            
            <div className="space-y-4">
              {serviceHistory.map((service, index) => (
                <div
                  key={service.id}
                  className="p-4 border border-border/50 rounded-lg hover:bg-muted/20 transition-colors"
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <h4 className="font-arabic font-medium text-lg">{service.service}</h4>
                      <p className="text-muted-foreground font-arabic">{service.garage}</p>
                    </div>
                    <div className="text-left">
                      <p className="font-medium">{service.cost}</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(service.date).toLocaleDateString('ar-SA')}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Badge className="bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400">
                      {service.status}
                    </Badge>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="font-arabic"
                      onClick={() => alert('سيتم عرض تفاصيل الخدمة')}
                    >
                      عرض التفاصيل
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        )}

        {activeTab === "documents" && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="premium-card p-6">
              <h3 className="font-almarai font-bold text-xl mb-6">وثائق المركبة</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 border border-border/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <FileText className="w-5 h-5 text-primary" />
                    <span className="font-arabic">رخصة القيادة</span>
                  </div>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => alert('سيتم عرض الوثيقة')}
                  >
                    عرض
                  </Button>
                </div>
                
                <div className="flex items-center justify-between p-3 border border-border/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <FileText className="w-5 h-5 text-primary" />
                    <span className="font-arabic">بوليصة التأمين</span>
                  </div>
                  <Button variant="ghost" size="sm">
                    عرض
                  </Button>
                </div>
                
                <div className="flex items-center justify-between p-3 border border-border/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <FileText className="w-5 h-5 text-primary" />
                    <span className="font-arabic">شهادة الفحص الدوري</span>
                  </div>
                  <Button variant="ghost" size="sm">
                    عرض
                  </Button>
                </div>
              </div>
            </Card>

            <Card className="premium-card p-6">
              <h3 className="font-almarai font-bold text-xl mb-6">إجراءات سريعة</h3>
              
              <div className="space-y-3">
                <BookingModal garageName="كراج معتمد">
                  <Button className="w-full btn-premium text-white font-arabic justify-start" size="lg">
                    <Calendar className="w-5 h-5 ml-3" />
                    حجز موعد صيانة
                  </Button>
                </BookingModal>
                
                <Button 
                  variant="outline" 
                  className="w-full font-arabic justify-start" 
                  size="lg"
                  onClick={() => navigate('/invoices')}
                >
                  <CreditCard className="w-5 h-5 ml-3" />
                  عرض الفواتير
                </Button>
                
                <Button 
                  variant="outline" 
                  className="w-full font-arabic justify-start" 
                  size="lg"
                >
                  <Settings className="w-5 h-5 ml-3" />
                  إعدادات المركبة
                </Button>
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}