import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { StatsCard, StatsGrid } from "@/components/ui/stats-card"
import { LoadingPage } from "@/components/ui/loading-spinner"
import { EmptyState } from "@/components/ui/empty-state"
import { StatusBadge } from "@/components/ui/status-badge"
import { useAuthContext } from "@/contexts/AuthContext"
import { useVehiclesByCustomer } from "@/hooks/useVehicles"
import { useOrdersByCustomer } from "@/hooks/useOrders"
import { Car, Calendar, Clock, Plus, Wrench, FileText } from "lucide-react"
import { Link } from "react-router-dom"

export function CustomerDashboard() {
  const { user } = useAuthContext()
  
  const { data: vehicles, isLoading: vehiclesLoading } = useVehiclesByCustomer(user?.id || '')
  const { data: orders, isLoading: ordersLoading } = useOrdersByCustomer(user?.id || '')

  if (vehiclesLoading || ordersLoading) {
    return <LoadingPage text="جاري تحميل لوحة التحكم..." />
  }

  const activeVehicles = vehicles?.filter(v => v.status === 'active').length || 0
  const pendingOrders = orders?.filter(o => o.status === 'pending').length || 0
  const inProgressOrders = orders?.filter(o => o.status === 'in_progress').length || 0
  const completedOrders = orders?.filter(o => o.status === 'completed').length || 0

  const recentOrders = orders?.slice(0, 5) || []

  return (
    <div className="space-y-6 p-6">
      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">مرحباً، {user?.full_name || 'عزيزي العميل'}</h1>
          <p className="text-muted-foreground">
            إدارة مركباتك وطلبات الصيانة من مكان واحد
          </p>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link to="/customer/vehicles">
              <Plus className="ml-2 h-4 w-4" />
              إضافة مركبة
            </Link>
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <StatsGrid>
        <StatsCard
          title="المركبات النشطة"
          value={activeVehicles}
          description="من إجمالي المركبات"
          icon={Car}
        />
        <StatsCard
          title="الطلبات المعلقة"
          value={pendingOrders}
          description="في انتظار الموافقة"
          icon={Clock}
        />
        <StatsCard
          title="قيد التنفيذ"
          value={inProgressOrders}
          description="طلبات جاري العمل عليها"
          icon={Wrench}
        />
        <StatsCard
          title="الطلبات المكتملة"
          value={completedOrders}
          description="هذا الشهر"
          icon={FileText}
        />
      </StatsGrid>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Recent Orders */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              الطلبات الأخيرة
            </CardTitle>
            <CardDescription>
              آخر 5 طلبات صيانة
            </CardDescription>
          </CardHeader>
          <CardContent>
            {recentOrders.length > 0 ? (
              <div className="space-y-4">
                {recentOrders.map((order) => (
                  <div key={order.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="space-y-1">
                      <p className="font-medium">{order.garage?.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {order.vehicle?.make} {order.vehicle?.model} - {order.vehicle?.license_plate}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(order.created_at).toLocaleDateString('ar-SA')}
                      </p>
                    </div>
                    <div className="text-left">
                      <StatusBadge status={order.status} />
                      {order.total_amount && (
                        <p className="text-sm font-medium mt-1">
                          {order.total_amount} ر.س
                        </p>
                      )}
                    </div>
                  </div>
                ))}
                <Button variant="outline" className="w-full" asChild>
                  <Link to="/customer/orders">عرض جميع الطلبات</Link>
                </Button>
              </div>
            ) : (
              <EmptyState
                icon={Calendar}
                title="لا توجد طلبات"
                description="لم تقم بإنشاء أي طلبات صيانة بعد"
                action={{
                  label: "تصفح الورش",
                  onClick: () => window.location.href = "/customer/garages"
                }}
              />
            )}
          </CardContent>
        </Card>

        {/* My Vehicles */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Car className="h-5 w-5" />
              مركباتي
            </CardTitle>
            <CardDescription>
              إدارة مركباتك المسجلة
            </CardDescription>
          </CardHeader>
          <CardContent>
            {vehicles && vehicles.length > 0 ? (
              <div className="space-y-4">
                {vehicles.slice(0, 3).map((vehicle) => (
                  <div key={vehicle.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="space-y-1">
                      <p className="font-medium">
                        {vehicle.make} {vehicle.model} ({vehicle.year})
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {vehicle.license_plate}
                      </p>
                      {vehicle.color && (
                        <p className="text-xs text-muted-foreground">
                          اللون: {vehicle.color}
                        </p>
                      )}
                    </div>
                    <StatusBadge status={vehicle.status} />
                  </div>
                ))}
                <Button variant="outline" className="w-full" asChild>
                  <Link to="/customer/vehicles">إدارة المركبات</Link>
                </Button>
              </div>
            ) : (
              <EmptyState
                icon={Car}
                title="لا توجد مركبات"
                description="أضف مركبتك الأولى لبدء استخدام الخدمة"
                action={{
                  label: "إضافة مركبة",
                  onClick: () => window.location.href = "/customer/vehicles"
                }}
              />
            )}
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>الإجراءات السريعة</CardTitle>
          <CardDescription>
            الوصول السريع للخدمات الأكثر استخداماً
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <Button variant="outline" className="h-20 flex-col gap-2" asChild>
              <Link to="/customer/garages">
                <Wrench className="h-6 w-6" />
                تصفح الورش
              </Link>
            </Button>
            <Button variant="outline" className="h-20 flex-col gap-2" asChild>
              <Link to="/customer/vehicles">
                <Car className="h-6 w-6" />
                إدارة المركبات
              </Link>
            </Button>
            <Button variant="outline" className="h-20 flex-col gap-2" asChild>
              <Link to="/customer/invoices">
                <FileText className="h-6 w-6" />
                الفواتير
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
