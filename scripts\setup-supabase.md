# Supabase Setup Guide

## 1. Database Migration

Run the migration to create all tables and RLS policies:

```bash
# If you have Supabase CLI installed
supabase db push

# Or manually run the SQL in Supabase Dashboard
# Copy the content from supabase/migrations/20241210000001_initial_schema.sql
# and run it in the SQL Editor
```

## 2. Create Admin and Garage Owner Accounts

Since only customers can sign up through the app, you need to manually create admin and garage owner accounts:

### In Supabase Auth Dashboard:

1. Go to Authentication > Users
2. Click "Add user"
3. Create the following users:

**Admin Account:**
- Email: `<EMAIL>`
- Password: `admin123456`
- User Metadata:
```json
{
  "role": "admin",
  "full_name": "مدير النظام"
}
```

**Garage Owner Accounts:**
- Email: `<EMAIL>`
- Password: `owner123456`
- User Metadata:
```json
{
  "role": "garage_owner",
  "full_name": "مالك مركز الخليج"
}
```

- Email: `<EMAIL>`
- Password: `owner123456`
- User Metadata:
```json
{
  "role": "garage_owner",
  "full_name": "مالك النجم الذهبي"
}
```

- Email: `<EMAIL>`
- Password: `owner123456`
- User Metadata:
```json
{
  "role": "garage_owner",
  "full_name": "مالك مركز الشرق"
}
```

## 3. Seed Sample Data

Run the seed data to populate garages and services:

```sql
-- Copy and run the content from supabase/seed.sql in the SQL Editor
```

## 4. Update Garage Owner IDs

After creating the garage owner accounts, update the garages table with their user IDs:

```sql
-- Replace USER_ID_FROM_AUTH with actual user IDs from the auth.users table
UPDATE public.garages SET owner_id = 'USER_ID_FROM_AUTH' WHERE email = '<EMAIL>';
UPDATE public.garages SET owner_id = 'USER_ID_FROM_AUTH' WHERE email = '<EMAIL>';
UPDATE public.garages SET owner_id = 'USER_ID_FROM_AUTH' WHERE email = '<EMAIL>';
```

## 5. Configure Google OAuth (Optional)

1. Go to Authentication > Providers
2. Enable Google provider
3. Add your Google OAuth credentials
4. Set redirect URL to: `http://localhost:5173/auth/callback`

## 6. Environment Variables

Make sure your `.env` file has the correct Supabase credentials:

```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 7. Test the Setup

1. Start the development server:
```bash
npm run dev
```

2. Test the following:
   - Customer signup at `/auth/signup`
   - Login with admin account at `/auth/login`
   - Login with garage owner account at `/auth/login`
   - Customer login and dashboard access

## 8. Role-Based Access

The system will automatically redirect users based on their roles:
- **Admin** → `/admin`
- **Garage Owner** → `/garage`
- **Customer** → `/customer`

## 9. RLS Policies

The database includes comprehensive Row-Level Security policies:
- Users can only see their own data
- Garage owners can manage their own garages and services
- Admins have full access to all data
- Customers can view active garages and services

## 10. Troubleshooting

### Common Issues:

1. **Profile not created after signup:**
   - Check if the trigger `on_auth_user_created` is working
   - Manually create profile if needed

2. **RLS blocking queries:**
   - Ensure user is properly authenticated
   - Check if user has the correct role

3. **Google OAuth not working:**
   - Verify redirect URL is correct
   - Check Google OAuth credentials

### Useful SQL Queries:

```sql
-- Check all users and their roles
SELECT u.email, p.role, p.full_name 
FROM auth.users u 
LEFT JOIN public.profiles p ON u.id = p.id;

-- Check garage ownership
SELECT g.name, p.full_name as owner_name, p.email 
FROM public.garages g 
LEFT JOIN public.profiles p ON g.owner_id = p.id;

-- Reset user role
UPDATE public.profiles SET role = 'admin' WHERE email = '<EMAIL>';
```
